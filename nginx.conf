user root;			                      #配置用户或者组
worker_processes auto;                            #设置子进程数，设置为自动，根据CPU核数确定
worker_rlimit_nofile 65535;                       #与ulimit -n 的值保持一致
events
{
	multi_accept on;                              #设置一个进程是否同时接受多个网络连接，默认为off
	use epoll;                                    #高性能网络I/O模型，不用管
	worker_connections 65535;                     #单个进程最大连接数（最大连接数=连接数*进程数），与ulimit -n 的值一致即可
}

http{
	#基本设置
	server_tokens off;                            #关闭在错误页面中的nginx版本数字
	sendfile on;                                  #进行下载等应用磁盘IO重负载应用，可设置为 off
	underscores_in_headers on;                    #开启支持下划线的header
	include       mime.types;                     #调用样式
	default_type application/octet-stream;

	#日志设置
	error_log /var/log/nginx/error.log crit;    #记录严重的错误，注意修改路径
	log_format access escape=json '$remote_addr - $server_port - $remote_user [$time_local] "$request" '
	'$status $body_bytes_sent "$http_referer" $request_body '
	'"$http_host" "$http_user_agent" $http_x_forwarded_for "$request_time" $upstream_addr';
	access_log /var/log/nginx/access.log access;#记录访问日志，上面为日志访问格式

	#连接设置
	#keepalive_timeout 300;                       #长连接超时时间，对于扫描或有需要尝试建立连接的需要打开
	#keepalive_requests 500;                       #每个长连接最大请求数
	server_names_hash_bucket_size 128;            #服务器名字的hash表大小
	client_header_buffer_size 32k;                #客户端请求头部的缓冲区大小，默认这样就行
	large_client_header_buffers 4 64k;            #客户请求头缓冲大小，默认就行
	client_max_body_size 300m;                    #设定通过nginx上传文件的大小，需要上传文件的注意这个参数

	#压缩设置
	gzip on;                                      #采用数据压缩
	gzip_min_length 1k;                           #最小压缩文件大小
	gzip_buffers     4 16k;                       #压缩缓冲区
	gzip_http_version 1.0;                        #压缩版本
	gzip_comp_level 4;                            #压缩等级
	gzip_types       text/plain application/x-javascript text/css application/xml;#压缩类型
	gzip_vary on;

	#缓存cache参数配置
	proxy_connect_timeout 5;                      #与后端程序连接超时时间，单位为秒
	proxy_read_timeout 60;                        #读取后端程序超时时间，扫码业务设置为1800，注意
	proxy_send_timeout 5;                         #向后端发送超时时间
	proxy_buffer_size 16k;
	proxy_buffers 4 64k;
	proxy_busy_buffers_size 128k;
	proxy_temp_file_write_size 128k;
	proxy_headers_hash_max_size 51200;            #设置头部哈希表的最大值，不能小于你后端服务器设置的头部总数（不明确意思）
	proxy_headers_hash_bucket_size 6400;          #设置头部哈希表大小（不明确意思）

	upstream bas-api {
      server *************:8443;                          #设置bas-api服务地址
      keepalive 16;                                     #启动后端长连接
  }

	server {
		listen 80;                                      #监听端口
		server_name localhost;                            #监听地址，域名可以有多个，用空格隔开，一般填写域名、IP，可配 置阻止其他server_name访问
		root /usr/share/nginx/html;
		index index.html index.htm index.nginx-debian.html;             #默认页

		location ~* /(portal/admin|html|js|css|fonts)/ {
			#配置安全访问
			proxy_set_header Host  $host:$server_port;    #修改Host头为用户真实Host，不修改这会统一发送代理服务器的Host，后端服务会认为都是代理服务器发送的请求。
			proxy_set_header X-Real-IP $remote_addr;      #使后端服务拿到用户真实IP
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;#获取用户真实IP地址
			proxy_redirect off;                           #作用是对发送给客户端的URL进行修改！off设置为关闭此功能http://$host:8200/   http://$http_host/;
			proxy_http_version 1.1;                       #设置Http协议版本
			proxy_pass http://*************:9091;                    #转向定义服务列表，如果只做代理可以直接设置为http://127.0.0.1:10000
		}

		location / {
			add_header Set-Cookie redirectUrl=$scheme://$host:$server_port$request_uri;
			rewrite ^(.*)$ /index.html break;
			expires -1;
		}

		location ~* ^.+\.(js|ico|html|htm|png|jpg|css|svg|mp3|eot|ttf|woff)$ {
			proxy_redirect     off;
			expires 7d;
		}

		location /api {
			proxy_pass http://*************:8004;
			proxy_redirect     off;
			proxy_set_header   Host             $host;
			proxy_set_header   X-Real-IP        $remote_addr;
			proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
		}

		location /portal-conversion/ {
			proxy_pass http://*************:8004/;
			proxy_redirect     off;
			proxy_set_header   Host             $host;
			proxy_set_header   X-Real-IP        $remote_addr;
			proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
		}

		location /radacct-service {
			proxy_pass http://*************:8020;
			proxy_redirect     off;
			proxy_set_header   Host             $host;
			proxy_set_header   X-Real-IP        $remote_addr;
			proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
		}
		location /notification-service/ {
			proxy_pass http://*************:7001/;
			proxy_redirect     off;
			proxy_set_header   Host             $host;
			proxy_set_header   X-Real-IP        $remote_addr;
			proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
		}

    location /api-service/ {
          proxy_pass http://bas-api/;
          proxy_redirect     off;
          proxy_set_header   Host             $host;
          proxy_set_header   X-Real-IP        $remote_addr;
          proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
    }

	}
}