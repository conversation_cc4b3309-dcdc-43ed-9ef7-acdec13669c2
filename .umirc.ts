import { defineConfig } from '@umijs/max';
import theme from './src/theme';
export default defineConfig({
  theme: theme,
  define: {
    'process.env': {
      DETECT_URL: 'http://oss.wenet.com.cn/captive-portal/connection-test.js',
      DETECT_TIMEOUT_MS: 3000,
      UMI_ENV: 'prod',
    },
  },
  fastRefresh: true,
  model: {},
  initialState: {},
  antd: {},
  reactQuery: {
    devtool: true,
    queryClient: {},
  },
  conventionRoutes: {
    exclude: [/\/components\//, /\/models\//],
  },
  jsMinifier: 'terser',
  targets: {
    ie: 10,
    firefox: 45,
    safari: 10,
    edge: 13,
    ios: 10,
  },
  proxy: {
    '/portal-conversion': {
      target: 'http://192.168.0.190:8004',
      // target: 'http://192.168.15.162:8004',
      // "target": "http://192.168.48.103:8001",
      changeOrigin: true,
      pathRewrite: { '^/portal-conversion': '' },
    },
    '/radacct-service': {
      target: 'http://192.168.0.190:8020',
      // target: 'http://192.168.15.162:8020',
      // target: 'http://192.168.48.91:8020',
      changeOrigin: true,
      headers: {
        Authorization: 'Basic cmFkYWNjdC1hZG1pbjpzM2NyM3QuYWRtaW4ucmFkYWNjdA==',
      },
      pathRewrite: { '^/radacct-service': '' },
    },
    '/api-service': {
      // target: 'http://*************:8443',
      target: 'https://gw.waf.bas.dev.wenet.group',
      changeOrigin: true,
      pathRewrite: { '^/api-service': '' },
    },
    '/notification-service': {
      target: 'https://gw.waf.bas.dev.wenet.group',
      changeOrigin: true,
    },
  },
  hash: true,
  tailwindcss: {},
  headScripts: ['/lib/wlQrcodeLogin.js'],
  scripts: [
    {
      src: '/lib/rc-util-patch.js',
    },
    {
      src: '/lib/hello.js',
    },
  ],
});
