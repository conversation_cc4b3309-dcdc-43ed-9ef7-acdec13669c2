{"name": "portal-web-nb", "version": "0.2.3", "private": true, "scripts": {"build": "max build && node scripts/post-build.js", "postinstall": "max setup", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "start": "max dev"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/icons": "^5.6.1", "@types/qrcode": "^1.5.0", "@umijs/max": "^4.0.83", "antd": "^4.24.14", "axios": "^1.11.0", "copy-to-clipboard": "^3.3.1", "crypto-js": "^4.1.1", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.29.3", "qrcode": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "ua-parser-js": "^1.0.37"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@types/crypto-js": "^4.1.1", "@types/js-cookie": "^3.0.1", "@types/lodash": "^4.14.181", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.0.0", "@umijs/test": "^3.5.22", "core-js": "^3.41.0", "cross-env": "^7.0.3", "eslint": "8.2.0", "lint-staged": "^12.3.7", "prettier": "^2.6.2", "tailwindcss": "^3.2.7", "typescript": "^4.6.3", "yorkie": "^2.0.0"}, "gitHooks": {"pre-commit": "lint-staged"}}