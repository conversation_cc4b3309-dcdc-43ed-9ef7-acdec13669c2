const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const DIST_PATH = './dist';

// 动态匹配 UMI 主文件
function findUmiEntry() {
  const files = fs.readdirSync(DIST_PATH);
  return files.find((f) => /^umi\.[a-f0-9]{8}\.js$/.test(f));
}

// Babel 转译配置
function transpileFile(fileName) {
  const filePath = path.join(DIST_PATH, fileName);
  const tempPath = path.join(DIST_PATH, 'umi.tmp.js');

  execSync(`npx babel ${filePath} ` + `--presets=@babel/preset-env ` + `--plugins=@babel/plugin-transform-spread ` + `--out-file ${tempPath}`);

  // 保持原文件名
  fs.renameSync(tempPath, filePath);
  console.log(`成功处理文件: ${fileName}`);
}

// 主流程
const entryFile = findUmiEntry();
if (entryFile) {
  transpileFile(entryFile);
} else {
  console.error('未找到 UMI 主入口文件');
}
