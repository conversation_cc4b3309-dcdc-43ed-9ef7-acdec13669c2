declare module '*.css';
declare module '*.less';
declare module '*.png';
declare module '*.jpg';
declare module '*.svg' {
  export function ReactComponent(props: React.SVGProps<SVGSVGElement>): React.ReactElement;
  const url: string;
  export default url;
}

interface RadioFormType {
  name: string;
  type: 'radio';
  options?: {
    label: string;
    value: string;
  }[];
  default?: string;
  required: boolean;
  isCarry: boolean;
  emptyTip: string;
  rememberable: boolean;
}

interface SelectFormType {
  name: string;
  placeholder: string;
  type: 'select';
  options?: {
    label: string;
    value: string;
  }[];
  default?: string;
  required: boolean;
  isCarry: boolean;
  emptyTip: string;
  rememberable: boolean;
  searchable?: boolean;
  useRemoteOptions?: boolean;
  remoteOptionsConfig?: {
    listPath: string[];
    url: string;
    labelName: string;
    valueName: string;
    uniqueKey: string;
  };
  noDataTip?: string;
}

interface InputFormType {
  name: string;
  placeholder: string;
  type: 'input';
  prefixType: string;
  suffixType: string;
  prefixValue: string;
  suffixValue: string;
  required: boolean;
  isCarry: boolean;
  emptyTip: string;
  rememberable: boolean;
  icon?: string;
  inputRegex?: '';
  inputErrorTip?: '';
}

interface PasswordFormType {
  name: string;
  placeholder: string;
  type: 'password';
  required: boolean;
  isCarry: boolean;
  emptyTip: string;
  rememberable: boolean;
  icon?: string;
}

type AuthForm = SelectFormType | InputFormType | PasswordFormType | RadioFormType;
type AuthWayType = 'username' | 'wechat-mp' | 'sms-code' | 'unified';

type AuthByUsername = {
  type: 'username';
  label: string;
  tipsAtBottom?: string;
};

type AuthByWechatMp = {
  type: 'wechat-mp';
  label: string;
  disableAtMobile?: boolean;
};

type AuthBySmsCode = {
  type: 'sms-code';
  label: string;
  mobileInputPlaceholder: string;
  sendSmsCodeButtonText: string;
  smsCodeInputPlaceholder: string;
  smsCodeInputEmptyTip: string;
  smsCodeInputErrorTip: string;
  loginButtonText: string;
  smsCodeSendSuccessTip: string;
  smsCodeSendErrorTip: string;
  mobileInputErrorTip: string;
  mobileInputEmptyTip: string;
  mobileInputRegex: string;
  mobilePrefixType: string;
  mobilePrefixValue: string;
  mobileSuffixType: string;
  mobileSuffixValue: string;
  prependForm: AuthForm[];
};

type AuthByUnified = {
  type: 'unified';
  label: string;
  unifiedUrl: string;
  appType: string;
  tip1?: string;
  tipsAtBottom?: string;
};

type AuthByWeLinkQrCode = {
  enable: boolean;
  client_id: string;
  redirect_uri: string;
};

type AuthWay = AuthByUsername | AuthByWechatMp | AuthBySmsCode | AuthByUnified;
interface Modal {
  okButtonText: string;
  title: string;
  content: string;
  closeIcon: boolean;
  popUpCountEverySession: number;
  closeCountDown: number; // 秒
}

declare interface FullConfig {
  defaultSchoolName: string;
  configrations: PortalWebConfigration[];
}

declare interface PortalWebConfigration {
  schoolName: string;
  schoolNameEn: string;
  telexNumber: boolean;
  favicon: string;
  logo: string;
  logoPosition: 'left' | 'center' | 'right';
  isWebsiteGray: boolean;
  layout?: {
    showContact?: true;
    contact?: {
      imgUrl?: string;
      imgDescription?: string;
      content?: string;
    };
    footer?: {
      label: string;
      link: string;
    }[];
  };
  theme: {
    primaryColor?: string;
    infoColor?: string;
    successColor?: string;
    processingColor?: string;
    errorColor?: string;
    warningColor?: string;
  };
  colors: {
    themeColor: string;
    headerColor: string;
    contentBgColor: string;
    pcBgColor: string;
    pcBgImage: string;
  };
  browserTitle: string;
  announcement: {
    authPageAlert: string;
    successPageAlert: string;
    authPageNotify: string;
    successPageNotify: string;
  };
  authPage: {
    browserTitle: string;
    title: string;
    authForm: AuthForm[];
    agreeLimit: boolean;
    enableMultiDproxy?: boolean;
    multiDproxyConfig?: {
      modeRadio: {
        useMultiDproxyLabel: string;
        doNotUseMultiDproxyLabel: string;
        emptyTip: string;
      };
      multiDproxyAccount: {
        emptyTip: string;
        placeholder: string;
        noDataTip: string;
        unbindTip: string;
      };
    };
    authButtonText: string;
    rememberMeText: string;
    hidden7MoorCsBtn?: boolean;
    modal?: Modal;
    concurrentModal: {
      title: string;
      displaySessionInfo: ('deviceType' | 'macAddress')[];
      showDeviceName: { [key: string]: string };
    };
    actions: {
      noBillingPlan: 'topError' | 'arrearagePage'; // topError, arrearagePage
    };
    messages: {
      redirectError: string;
      noPlanBind: string;
      beLimited: string;
      expired: string;
      frozen: string;
      overConcurrency: string;
      invalidUsernameOrPassword: string;
      onlyOfficeZone: string;
      noBillingPlan: string;
    };
    authWays: AuthWay[];
    authByWeLinkQrCode: AuthByWeLinkQrCode;
    checkPasswordSafety: boolean;
    passwordReg: string;
  };
  successPage: {
    browserTitle: string;
    title: string;
    showPlan: boolean;
    showNetwork: boolean;
    networkInfo: {
      ip: boolean;
      username: boolean;
      acctTime: boolean;
    };
    links: {
      link: string;
      label: string;
      bold: boolean;
      blank?: boolean;
    }[];
    successBanner: string;
    showDisconnect: boolean;
    disconnectButtonText: string;
    redetectAfterDisconnectMs: number;
    detectDelayOnPageLoadMs: number;
    modal: Modal;
    disconnectWhoAreYouModal: {
      disable: boolean;
      title: string;
      tip: string;
      authForm: AuthForm[];
      cancelButtonText: string;
      okButtonText: string;
      messages: {
        invalidUsernameOrPassword: string;
      };
      authWays: AuthWay[];
    };
    disconnectConfirmModal: {
      title: string;
      tip: string;
      cancelButtonText: string;
      okButtonText: string;
    };
    checkRealname: boolean;
  };
  findPassowrdPage: {
    validate: {
      prependForm: AuthForm[];
      mobilePrefixType: string;
      mobilePrefixValue: string;
      mobileSuffixType: string;
      mobileSuffixValue: string;
    };
  };
  resetPasswordPage: {
    announcement: string;
    passwordReg: string;
    prependForm: AuthForm[];
    form: {
      webAuthUser: { description: string; errorTip: string; prefixType: string; prefixValue: string };
      webAuthPassword: { description: string; errorTip: string };
      webAuthNewPassword: { description: string; errorTip: string };
    };
  };
  arrearagePage: {
    browserTitle: string;
    html: string;
    markUpHtml: string;
    checkMobile: boolean;
    bindMobileTip: string;
  };
  findSuccessPage: {
    browserTitle: string;
    html: string;
    isCopy: boolean;
    copyText: string;
  };
  deviceListPage: {
    browserTitle: string;
    displaySessionInfo: ('deviceType' | 'macAddress')[];
    showReconnectButton: boolean;
    showDeviceName: { [key: string]: string };
    whoAreYouModal: {
      disable: boolean;
      title: string;
      tip: string;
      authForm: AuthForm[];
      cancelButtonText: string;
      okButtonText: string;
      messages: {
        invalidUsernameOrPassword: string;
      };
      authWays: AuthWay[];
    };
  };
  activityPages: {
    id: number;
    browserTitle: string;
    html: string;
  }[];
  userAgreementPage: {
    browserTitle: string;
    html: string;
  };
  privacyPolicyPage: {
    browserTitle: string;
    html: string;
  };
  systemConfig: {
    detectJsUrl: string;
    detectTimeoutMs: number;
    autoRedirectUrl: string;
    autoRedirecWhenNoRedirectInfo: boolean;
    autoRedirectDelay: number; // 因无重定向参数导致的认证失败后的自动跳转延迟时间
    showMarketingLandpageWhenSucess: boolean; // 认证成功后是否展示营销落地页
    marketingPageUrl: string;
    marketingTextInPC: string; // PC端营销文案
    iframeMessagePostUrl: string; // iframe推送消息地址
    devicetypeUaFirst: boolean; // 优先使用ua判断设备类型 默认 true
    tabletIsPc: boolean; // 平板设备当作pc  默认 true
    ad: boolean;
  };
}

declare interface PaginationResponse<T> {
  content: T[];
  number: number;
  size: number;
  totalPages: number;
  numberOfElements: number;
  totalElements: number;
  previousPage: boolean;
  first: boolean;
  nextPage: boolean;
  last: boolean;
  sort?: any;
}

type WelinkQrCode = {
  unique_id: string;
  qrcode_url: string;
  code: string;
  message: string;
};

declare interface Window {
  /**
   * 探测成功后的回调函数
   */
  onConnectionTestSuccess: () => void;

  /**
   * 华为WeLink生成二维码方法
   */
  wlQrcodeLogin: (params: {
    id: string;
    client_id: string;
    response_type: 'code';
    redirect_uri: string;
    scope: string;
    state?: string;
    style?: string;
    width?: string;
    height?: string;
    self_redirect?: boolean;
  }) => WelinkQrCode;
}
