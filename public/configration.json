{"defaultSchoolName": "wenet", "configrations": [{"schoolName": "新路网络", "schoolNameEn": "wenet", "telexNumber": "nroad", "browserTitle": "wenet无线认证", "logo": "/logo/wenet.svg", "logoPosition": "left", "theme": {"primaryColor": "#40a9ff", "infoColor": "#13c2c2", "successColor": "#95de64", "processingColor": "#40a9ff", "errorColor": "#f5222d", "warningColor": "#fa8c16"}, "colors": {"headerColor": "#ffffff", "contentBgColor": "#ffffff", "pcBgColor": "#f5f5f5", "pcBgImage": "", "pcBgBlur": false}, "authPage": {"browserTitle": "wenet无线认证", "title": "连接上网", "authForm": [{"name": "webAuthUser", "placeholder": "账号", "type": "input", "prefixType": "none", "suffixType": "none", "prefixValue": "school", "suffixValue": "", "required": true, "isCarry": true, "emptyTip": "请输入账号", "rememberable": true, "icon": "UserOutlined"}, {"name": "webAuthPassword", "placeholder": "密码", "type": "password", "required": true, "isCarry": true, "emptyTip": "请输入密码", "rememberable": true, "icon": "KeyOutlined"}], "multiDproxyConfig": {"modeRadio": {"useMultiDproxyLabel": "运营商1", "doNotUseMultiDproxyLabel": "校园网2"}, "multiDproxyAccount": {"emptyTip": "请选择运营商账号", "placeholder": "选择运营商账号", "noDataTip": "无数据，请检查您输入的学号", "unbindTip": "未绑定账号"}}, "authButtonText": "连接", "rememberMeText": "记住密码", "bottomLinks": [{"label": "校园网使用手册", "link": "/wenet/notify", "bold": true}, {"label": "隐私政策", "link": "/wenet/privacy-policy"}, {"label": "外链方式", "link": "https://www.baidu.com", "blank": true}, {"label": "找回密码", "link": "/wenet/findPassword", "blank": false}], "modal": {"cancelButtonText": "关闭", "okButtonText": "我知道了", "title": "校园网免费领取通知", "content": "<p style=\"text-align:center;\" align=\"center\"><strong><span style=\"font-size:30px\">校园网流量<span style=\"color:#c0392b\">免费</span>领取！</span></strong></p><p style=\"text-align:center;\" size=\"0\" _root=\"undefined\" __ownerID=\"undefined\" __hash=\"undefined\" __altered=\"false\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:20px\">关注公众号【WeNet服务中心】-【购买套餐】-领取免费套餐</span></span></strong></p><p></p><div class=\"media-wrap image-wrap align-center\" style=\"text-align:center\"><img src=\"http://portal-notify.wenet.com.cn/public/img/upload/2024111_1711433994503.png\" width=\"243\" height=\"243\" style=\"width:243px;height:243px\"/></div><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p><p style=\"text-align:center;\" align=\"center\"><strong><span style=\"color:#c0392b\"><span style=\"font-size:24px\">可截图微信扫码进入公众号</span></span></strong></p>", "closeIcon": true, "popUpCountEverySession": 0, "closeCountDown": 3}, "concurrentModal": {"title": "连接失败，超出并发", "displaySessionInfoType": "deviceType", "displaySessionInfo": ["deviceType", "<PERSON><PERSON><PERSON><PERSON>"], "showDeviceName": {"pc": "电脑", "mobile": "手机", "pad": "手机", "iphone": "手机", "android": "手机", "ios": "手机"}}, "messages": {"redirectError": "重定向参数错误，请在页面刷新后重试", "noPlanBind": "当前套餐未绑定手机号，前往【wenet服务中心-我的网络-我的套餐-绑定运营商账号】绑定手机号", "beLimited": "当前时间，网络访问被限制", "expired": "当前套餐已过期", "frozen": "当前账号被冻结，请联系客服", "overConcurrency": "已有设备在线", "invalidUsernameOrPassword": "你输入的账号或密码不正确", "onlyOfficeZone": "该账号仅允许在办公区域使用", "noBillingPlan": "未购买套餐"}, "authWays": [{"type": "unified", "label": "统一身份认证", "unifiedUrl": "https://authserver.xagdyz.com/authserver/oauth2.0/authorize?response_type=code&client_id=1305918699289608192&redirect_uri=http%3A%2F%2F*************%3A6999%2Fwenet%2Funified-callback&state=1", "appType": "JINZHI"}, {"label": "短信验证", "type": "sms-code", "mobileInputPlaceholder": "手机号码", "sendSmsCodeButtonText": "发送验证码", "smsCodeInputPlaceholder": "输入验证码", "smsCodeInputEmptyTip": "验证码不能为空", "smsCodeInputErrorTip": "验证码错误", "loginButtonText": "登录", "smsCodeSendSuccessTip": "验证码已发送", "smsCodeSendErrorTip": "发送失败，请稍后重试", "mobileInputErrorTip": "请输入正确的手机号", "mobileInputEmptyTip": "请输入手机号", "mobileInputRegex": "^1[0-9]{10}$"}, {"label": "账号密码", "type": "username"}], "authByWeLinkQrCode": {"enable": true, "client_id": "20240422160041344621130", "redirect_uri": "http://*************:6999"}, "checkPasswordSafety": false, "passwordReg": "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$"}, "successPage": {"browserTitle": "连接成功,关注公众号查询更多信息", "title": "连接成功", "showPlan": true, "showNetwork": true, "networkInfo": {"username": true, "ip": true, "acctTime": true}, "links": [{"label": "校园网使用手册", "link": "/wenet/notify", "bold": true}, {"label": "在线设备管理", "link": "/wenet/device-list"}], "successBanner": "", "showDisconnect": true, "disconnectButtonText": "断开连接", "redetectAfterDisconnectMs": 1000, "detectDelayOnPageLoadMs": 5000, "disconnectWhoAreYouModal": {"disable": true, "title": "身份验证", "tip": "请输入您的账号信息，查询到此账号会话列表后点击相应设备即可下线！", "authForm": [{"name": "webAuthUser", "placeholder": "用户名", "type": "input", "prefixType": "none", "suffixType": "none", "prefixValue": "school", "suffixValue": "", "required": true, "isCarry": true, "emptyTip": "请输入用户名", "rememberable": true, "icon": "UserOutlined"}, {"name": "webAuthPassword", "placeholder": "密码", "type": "password", "required": true, "isCarry": true, "emptyTip": "请输入密码", "rememberable": true, "icon": "KeyOutlined"}], "cancelButtonText": "取消", "okButtonText": "确认并断开", "authWays": [{"label": "账号密码", "type": "username"}, {"label": "手机号验证码", "type": "sms-code", "mobileInputPlaceholder": "手机号码", "sendSmsCodeButtonText": "发送验证码", "smsCodeInputPlaceholder": "输入验证码", "smsCodeInputEmptyTip": "验证码不能为空", "smsCodeInputErrorTip": "验证码错误", "loginButtonText": "登录", "smsCodeSendSuccessTip": "验证码已发送", "smsCodeSendErrorTip": "发送失败，请稍后重试", "mobileInputErrorTip": "请输入正确的手机号", "mobileInputEmptyTip": "请输入手机号", "mobileInputRegex": "^1[0-9]{10}$"}]}, "disconnectConfirmModal": {"title": "确认断开？", "tip": "确认断开当前设备网络吗", "cancelButtonText": "取消", "okButtonText": "确认并断开", "messages": {"invalidUsernameOrPassword": "用户名或密码错误"}}, "checkRealname": true}, "resetPasswordPage": {"announcement": "您的密码安全性较低，请及时修改密码", "passwordReg": "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$", "form": {"webAuthUser": {"description": "学号", "errorTip": "请输入正确的学号"}, "webAuthPassword": {"description": "旧密码，默认身份证后6位", "errorTip": "请输入旧密码"}, "webAuthNewPassword": {"description": "密码必须包含数字和字母，长度8-16位", "errorTip": "密码必须包含数字和字母，长度8-16位"}}}, "arrearagePage": {"browserTitle": "连接失败，无可用套餐", "checkMobile": true, "bindMobileTip": "现在提交手机号，就可以领取免费套餐哦～", "html": "<div style=\"margin-top: 32px;\"><div style=\"font-size: 20px; text-align: center; margin-bottom: 32px; font-weight: bold;\">你没有可用套餐，<br />关注公众号进行购买</div><p style=\"color: #9ca3af; text-align: center; margin-left: 24px; margin-right: 24px; margin-bottom: 24px;\">【温馨提示：若您已办理含校园网的新卡，请前往WeNet服务中心公众号-自助服务-激活宽带进行校园网激活】</p><div style=\"display: flex; flex: 1; align-items: center; justify-content: space-between; flex-direction: column;\"><img src=\"http://*************:6999/arrearage/QRcode.png\" alt=\"ORcode\" style=\"width: 198px; margin-bottom: 24px;\"></div></div>", "markUpHtml": "<div style=\"margin-top: 32px;\"><div style=\"font-size: 20px; text-align: center; margin-bottom: 64px; font-weight: bold;\">您的激活信息已失效，<br />请使用微信扫描下方二维码重新激活~</div><div style=\"display: flex; flex: 1; align-items: center; justify-content: center; flex-direction: column;\"><img src=\"http://*************:6999/arrearage/QRcode.png\" alt=\"ORcode\" style=\"width: 198px;\"></div></div>"}, "findSuccessPage": {"browserTitle": "找回密码成功,关注公众号查询更多信息", "isCopy": true, "copyPath": "https://mp.wenet.com.cn/", "html": ""}, "deviceListPage": {"browserTitle": "在线设备管理", "displaySessionInfo": ["<PERSON><PERSON><PERSON><PERSON>", "deviceType"], "showReconnectButton": false, "showDeviceName": {"pc": "电脑", "mobile": "手机", "pad": "手机", "iphone": "手机", "android": "手机", "ios": "手机"}, "whoAreYouModal": {"disable": true, "title": "断开确认", "tip": "确认断开当前设备网络吗", "authForm": [{"name": "school", "placeholder": "选择学校,支持搜索", "type": "select", "required": true, "isCarry": false, "emptyTip": "请选择学校", "rememberable": true, "searchable": true, "useRemoteOptions": true, "remoteOptionsConfig": {"listPath": [], "url": "/api-service/user/v1/projection/org/visible", "labelName": "localityName", "valueName": "telexNumber", "uniqueKey": "ou"}}, {"name": "webAuthUser", "placeholder": "用户名", "type": "input", "prefixType": "form", "suffixType": "none", "prefixValue": "school", "suffixValue": "", "required": true, "isCarry": true, "emptyTip": "请输入用户名", "rememberable": true, "icon": "UserOutlined"}, {"name": "webAuthPassword", "placeholder": "密码", "type": "password", "required": true, "isCarry": true, "emptyTip": "请输入密码", "rememberable": true, "icon": "KeyOutlined"}], "cancelButtonText": "取消", "okButtonText": "确认", "authWays": [{"label": "短信验证", "type": "sms-code", "mobileInputPlaceholder": "手机号码", "sendSmsCodeButtonText": "发送验证码", "smsCodeInputPlaceholder": "输入验证码", "smsCodeInputEmptyTip": "验证码不能为空", "smsCodeInputErrorTip": "验证码错误", "loginButtonText": "登录", "smsCodeSendSuccessTip": "验证码已发送", "smsCodeSendErrorTip": "发送失败，请稍后重试", "mobileInputErrorTip": "请输入正确的手机号", "mobileInputEmptyTip": "请输入手机号", "mobileInputRegex": "^1[0-9]{10}$"}, {"label": "账号密码", "type": "username"}]}}, "activityPages": [], "systemConfig": {"detectJsUrl": "http://oss.wenet.com.cn/captive-portal/connection-test.js1", "detectTimeoutMs": "1000", "autoRedirectUrl": "http://*******", "autoRedirecWhenNoRedirectInfo": true, "autoRedirectDelay": 1, "showMarketingLandpageWhenSucess": true, "marketingPageUrl": "http://m.wenet.com.cn/packageMarketing/pages/landing/landing?p=58a7328f&t=c", "marketingTextInPC": "WeNet校园网学期优惠活动已开启，快使用微信APP扫描下方二维码看看吧~", "enableAccurateDeviceType": false, "tabletIsPc": false, "devicetypeUaFirst": false, "ad": true}}]}