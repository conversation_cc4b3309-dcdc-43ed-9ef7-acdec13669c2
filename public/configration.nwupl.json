{"defaultSchoolName": "nwupl", "configrations": [{"schoolName": "nwupl", "schoolNameEn": "nwupl", "browserTitle": "连接上网", "logo": "/logo/nwupl.svg", "logoPosition": "left", "layout": {"showContact": true, "contact": {"imgUrl": "/images/nwupl-login-qrcode.png", "imgDescription": "Wenet服务中心", "content": "请扫描左侧二维码，关注WeNet服务中心公众号，点击“购买套餐”了解最新校园网信息。"}, "footer": [{"label": "校园网须知", "link": "/wenet/help"}, {"label": "校园网使用手册", "link": "/wenet/notify"}, {"label": "隐私政策", "link": "/wenet/privacy-policy"}]}, "theme": {"primaryColor": "#008EFF", "infoColor": "#13c2c2", "successColor": "#95de64", "processingColor": "#40a9ff", "errorColor": "#f5222d", "warningColor": "#fa8c16"}, "colors": {"headerColor": "#ffffff", "contentBgColor": "#ffffff", "pcBgColor": "#f5f5f5", "pcBgImage": "/background/nwupl-bg2.png"}, "authPage": {"browserTitle": "wenet无线认证", "title": "连接上网", "authForm": [{"name": "webAuthUser", "placeholder": "请输入工号（教工）/学号（学生）/身份证号（其他）", "type": "input", "prefixType": "none", "suffixType": "none", "prefixValue": "", "suffixValue": "", "required": true, "isCarry": true, "emptyTip": "请输入工号（教工）/学号（学生）/身份证号（其他）", "rememberable": true, "icon": "UserOutlined"}, {"name": "webAuthPassword", "placeholder": "请输入密码", "type": "password", "required": true, "isCarry": true, "emptyTip": "请输入密码", "rememberable": true, "icon": "KeyOutlined"}], "agreeLimit": false, "enableMultiDproxy": false, "multiDproxyConfig": {"modeRadio": {"useMultiDproxyLabel": "运营商1", "doNotUseMultiDproxyLabel": "校园网2"}, "multiDproxyAccount": {"emptyTip": "请选择运营商账号", "placeholder": "选择运营商账号", "noDataTip": "无数据，请检查您输入的学号", "unbindTip": "未绑定账号"}}, "authButtonText": "连接", "rememberMeText": "记住密码", "modal": {"popUpCountEverySession": 1, "okButtonText": "已阅读，并关闭", "closeIcon": false, "content": "", "closeCountDown": 10}, "concurrentModal": {"title": "连接失败，超出并发", "displaySessionInfoType": "deviceType", "displaySessionInfo": ["deviceType", "<PERSON><PERSON><PERSON><PERSON>"]}, "messages": {"redirectError": "重定向参数错误，请在页面刷新后重试", "noPlanBind": "当前套餐未绑定手机号，前往【wenet服务中心-我的网络-我的套餐-绑定运营商账号】绑定手机号", "beLimited": "当前时间，网络访问被限制", "expired": "当前套餐已过期", "frozen": "当前账号被冻结，请联系客服", "overConcurrency": "已有设备在线", "invalidUsernameOrPassword": "你输入的账号或密码不正确", "onlyOfficeZone": "该账号仅允许在办公区域使用", "noBillingPlan": "未购买套餐"}, "authWays": [{"label": "师生认证", "type": "unified", "unifiedUrl": "https://mp.wenet.com.cn/auth/unified", "tipsAtBottom": "<div><div>温馨提醒：</div><div>本校学生如统一身份认证失败也可通过访客认证方式登录</div></div>"}, {"label": "账号密码", "type": "username", "tipsAtBottom": "<div><div>温馨提醒：</div><div>本校学生如统一身份认证失败也可通过访客认证方式登录</div></div>"}, {"label": "短信验证", "type": "sms-code", "mobileInputPlaceholder": "手机号码", "sendSmsCodeButtonText": "发送验证码", "smsCodeInputPlaceholder": "输入验证码", "smsCodeInputEmptyTip": "验证码不能为空", "smsCodeInputErrorTip": "验证码错误", "loginButtonText": "登录", "smsCodeSendSuccessTip": "验证码已发送", "smsCodeSendErrorTip": "发送失败，请稍后重试", "mobileInputErrorTip": "请输入正确的手机号", "mobileInputEmptyTip": "请输入手机号", "mobileInputRegex": "^1[0-9]{10}$"}], "checkPasswordSafety": false, "passwordReg": "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$"}, "privacyPolicyPage": {"html": "<p>隐私政策</p>"}, "successPage": {"browserTitle": "连接成功", "title": "你已成功连接网络", "showPlan": true, "showNetwork": true, "networkInfo": {"username": true, "ip": true, "acctTime": true}, "links": [{"label": "在线设备管理", "link": "/wenet/device-list"}, {"label": "无感知绑定记录", "link": "/wenet/noSense"}], "successBanner": "<div style=\"text-align:center\"><img width=\"196px\" height=\"196px\" src=\"/images/nwupl-login-qrcode.png\"/><div style=\"margin-top: 8px;color:#ccdcdcd\">请扫描左侧二维码，关注WeNet服务中心公众号，点击“购买套餐”了解最新校园网信息</div></div>", "showDisconnect": true, "disconnectButtonText": "断开连接", "redetectAfterDisconnectMs": 1000, "disconnectWhoAreYouModal": {"title": "身份验证", "tip": "请输入您的账号信息，查询到此账号会话列表后点击相应设备即可下线！", "authForm": [{"name": "webAuthUser", "placeholder": "学号/身份证后八位", "type": "input", "prefixType": "none", "suffixType": "none", "prefixValue": "", "suffixValue": "", "required": true, "isCarry": true, "emptyTip": "学号/身份证后八位", "rememberable": true, "icon": "UserOutlined"}, {"name": "webAuthPassword", "placeholder": "初始密码:身份证后六位", "type": "password", "required": true, "isCarry": true, "emptyTip": "初始密码:身份证后六位", "rememberable": true, "icon": "KeyOutlined"}], "cancelButtonText": "取消", "okButtonText": "确认并断开", "authWays": [{"label": "账号密码", "type": "username"}]}, "disconnectConfirmModal": {"title": "确认断开？", "tip": "确认断开当前设备网络吗", "cancelButtonText": "取消", "okButtonText": "确认并断开", "messages": {"invalidUsernameOrPassword": "用户名或密码错误"}}}, "resetPasswordPage": {"announcement": "您的密码安全性较低，请及时修改密码", "passwordReg": "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$", "form": {"webAuthUser": {"description": "学号", "errorTip": "请输入正确的学号"}, "webAuthPassword": {"description": "旧密码，默认身份证后6位", "errorTip": "请输入旧密码"}, "webAuthNewPassword": {"description": "密码必须包含数字和字母，长度8-16位", "errorTip": "密码必须包含数字和字母，长度8-16位"}}}, "arrearagePage": {"browserTitle": "连接失败，无可用套餐", "isCopy": true, "copyPath": "https://mp.wenet.com.cn/", "buttonText": "立即购买", "selfLink": "https://mp.wenet.com.cn/", "html": "<p></p><div class=\"media-wrap image-wrap align-center\" style=\"text-align:center\"><img class=\"media-wrap image-wrap align-center\" src=\"http://*************:7001/public/img/upload/QRcode_1651111145971.png\"/></div><p></p><p style=\"text-align:center;\"><span style=\"color:#999999\">关注微信服务号</span></p><p style=\"text-align:center;\"><span style=\"color:#999999\">了解你的用网情况</span></p>"}, "findSuccessPage": {"browserTitle": "找回密码成功,关注公众号查询更多信息", "isCopy": true, "copyPath": "https://mp.wenet.com.cn/", "html": ""}, "deviceListPage": {"browserTitle": "在线设备管理", "displaySessionInfo": ["<PERSON><PERSON><PERSON><PERSON>", "deviceType"], "showReconnectButton": false, "whoAreYouModal": {"title": "身份验证", "tip": "请输入您的账号信息，查询此账号会话列表！", "authForm": [{"name": "webAuthUser", "placeholder": "学号/身份证后八位", "type": "input", "prefixType": "none", "suffixType": "none", "prefixValue": "", "suffixValue": "", "required": true, "isCarry": true, "emptyTip": "学号/身份证后八位", "rememberable": true, "icon": "UserOutlined"}, {"name": "webAuthPassword", "placeholder": "初始密码:身份证后六位", "type": "password", "required": true, "isCarry": true, "emptyTip": "初始密码:身份证后六位", "rememberable": true, "icon": "KeyOutlined"}], "cancelButtonText": "取消", "okButtonText": "确认", "authWays": [{"label": "账号密码", "type": "username"}]}}, "activityPages": [], "systemConfig": {"detectJsUrl": "http://oss.wenet.com.cn/captive-portal/connection-test.js", "detectTimeoutMs": "1000", "autoRedirectUrl": "http://*******", "autoRedirecWhenNoRedirectInfo": true, "autoRedirectDelay": 1, "showMarketingLandpageWhenSucess": false, "marketingPageUrl": "", "marketingTextInPC": "WeNet校园网学期优惠活动已开启，快使用微信APP扫描下方二维码看看吧~", "ad": true}}]}