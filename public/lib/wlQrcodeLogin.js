!(function (e, n) {
  e.wlQrcodeLogin = function (e) {
    var t,
      o = e.baseUrl ? e.baseUrl : 'https://login.welink.huaweicloud.com',
      i = n.createElement('iframe'),
      r = o + '/sso-proxy-front/public/qrcode/0.0.1/qrcode.html?redirect_uri=' + encodeURIComponent(e.redirect_uri) + '&client_id=' + e.client_id;
    (r += '&response_type=' + (e.response_type ? e.response_type : 'code')),
      (r += '&scope=' + (e.scope ? e.scope : 'snsapi_login')),
      (r += e.state ? '&state=' + e.state : ''),
      (r += e.style ? '&style=' + encodeURIComponent(e.style) : ''),
      (r += '&self_redirect=' + (e.self_redirect ? e.self_redirect : 'false')),
      (r += '&lang=' + (e.lang ? e.lang : 'cn')),
      (r += e.nameCN ? '&nameCN=' + encodeURIComponent(e.nameCN) : ''),
      (r += e.nameEN ? '&nameEN=' + encodeURIComponent(e.nameEN) : ''),
      (r += e.isHideName ? '&isHideName=true' : ''),
      (i.src = r),
      (i.frameBorder = '0'),
      (i.allowTransparency = 'true'),
      (i.scrolling = 'no'),
      (i.width = e.width ? e.width + 'px' : '365px'),
      (i.height = e.height ? e.height + 'px' : '400px'),
      ((t = n.getElementById(e.id)).innerHTML = ''),
      t.appendChild(i);
  };
})(window, document);
