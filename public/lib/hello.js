(function () {
  window.onload = function () {
    // 创建弹窗节点
    var modal = document.createElement('div');
    modal.style.cssText =
      'position: fixed;' +
      'top: 50%;' +
      'left: 50%;' +
      'margin-top: -150px;' + // 上边距为负弹窗高度的一半
      'margin-left: -150px;' + // 左边距为负弹窗宽度的一半
      'background-color: #ffffff;' +
      'border-radius: 8px;' +
      'border: 1px solid #cdcdcd;' +
      'padding: 20px;' +
      'width: 350px;' +
      'z-index: 1000;';

    // 标题
    var title = document.createElement('h3');
    title.innerText = '温馨提示';
    title.style.cssText = 'margin: 0 0 10px;' + 'font-size: 20px;' + 'font-weight: bold;' + 'color: #333333;';
    modal.appendChild(title);

    // 内容
    var content = document.createElement('p');
    content.innerText = '当前认证页面与浏览器不兼容，WeNet 已为您准备了火狐浏览器安装程序（无广告、不收费），无须网络便可安全安装，您是否确认安装？';
    content.style.cssText = 'margin: 0 0 20px;' + 'font-size: 16px;' + 'color: #666666;';
    modal.appendChild(content);

    // 按钮容器
    var buttonContainer = document.createElement('div');
    buttonContainer.style.marginTop = '20px';
    buttonContainer.style.textAlign = 'center';
    modal.appendChild(buttonContainer);

    // 不了，谢谢 按钮
    var cancelButton = document.createElement('a');
    cancelButton.innerText = '不了，谢谢';
    cancelButton.href = 'javascript:;';
    cancelButton.style.cssText =
      'padding: 8px 16px;' +
      'margin-right: 10px;' +
      'font-size: 14px;' +
      'color: #666666;' +
      'border: 1px solid #cccccc;' +
      'border-radius: 4px;' +
      'text-decoration: none;' + // 移除下划线
      'cursor: pointer;';
    cancelButton.onclick = function () {
      modal.parentNode.removeChild(modal);
    };
    buttonContainer.appendChild(cancelButton);

    // 立即安装 链接
    var installLink = document.createElement('a');
    installLink.innerText = '立即安装';
    installLink.href = '/Firefox_Setup_115.10.0esr.exe'; // 设置下载链接
    installLink.download = '火狐浏览器.exe'; // 设置下载文件名称
    installLink.style.cssText =
      'padding: 8px 16px;' +
      'font-size: 14px;' +
      'color: #ffffff;' +
      'background-color: #4CAF50;' +
      'border: none;' +
      'border-radius: 4px;' +
      'text-decoration: none;' + // 移除下划线
      'cursor: pointer;';
    buttonContainer.appendChild(installLink);

    if (document.documentMode) {
      document.body.appendChild(modal);
    }
  };
})();
