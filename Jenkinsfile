@Library('frontend-pipeline-library')_

podTemplate(containers: [
        containerTemplate(name: 'curl', image: 'harbor.nroad.com.cn/library/curl:7.79.1', command: 'cat', ttyEnabled: true, runAsUser: "1000"),
        containerTemplate(name: 'docker', image: 'harbor.nroad.com.cn/library/docker:latest', command: 'cat', ttyEnabled: true)
    ]) {
    node(POD_LABEL){
      stage('checkout') {
          checkout scm
            env.WORKSPACE = pwd()
            sh "git checkout ${TAG}"
            env.REGISTRY = "harbor.nroad.com.cn/bas"
            def projectInfo = getProjectInfo(TAG)
            env.APP_NAME = projectInfo.get('appName')
            env.APP_VERSION = projectInfo.get('appVersion')
            env.RELEASE_NOTE = projectInfo.get('releaseNote')
            env.COMMIT_MESSAGE = projectInfo.get('commitMessage')
      }

      stage('docker') {
          container('docker') {
              sh """
                docker --host ${DOCKER_HOST} build \\
                  --file ${env.WORKSPACE}/Dockerfile \\
                  --tag ${env.REGISTRY}/${env.APP_NAME}:${env.APP_VERSION} \\
                  --build-arg NPM_MIRROR=${NPM_MIRROR} \\
                  ${env.WORKSPACE}/
              """
              sh '''echo azp9oaJ7vfJhZpTsoUH4J64s7rdQUy8e | docker login --username robot\\$ci --password-stdin harbor.nroad.com.cn'''
              sh "docker --host ${DOCKER_HOST} push ${env.REGISTRY}/${env.APP_NAME}:${env.APP_VERSION}"
          }
      }

      stage('send') {
        def params = [
          imageUrl: "${env.REGISTRY}/${env.APP_NAME}:${env.APP_VERSION}",
          currentEnv: env.CURRENT_ENV,
          appName: env.APP_NAME,
          commitMessage: env.COMMIT_MESSAGE,
          releaseNote: env.RELEASE_NOTE,
          appVersion: env.APP_VERSION,
        ]
        sendToDingtalk(params)
      } 
    }
}
