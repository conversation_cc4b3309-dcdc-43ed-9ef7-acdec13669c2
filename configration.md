# portal-web 配置文件

## 配置示例

```javascript
{
  "defaultSchoolName": "wenet", // 当url没有学校参数时，自动选择的学校，填schoolNameEn
  "configrations": [  // 对每个学校单独做的配置
    {
      "schoolName": "wenet", // 学校名
      "schoolNameEn": "wenet", // 学校英文名
      "telexNumber": "nroad", // 学号前缀（与bas保持一致）
      "browserTitle": "wenet无线认证", // 全局的浏览器tab页标题，再其他页面没有配置的时候会显示
      "logo": "/logo/wenet.svg", // logo的地址，可以以 / 开头，也可以使用http、https开头
      "logoPosition": "left", // logo的位置，支持 left, right, center
      "backgroundColor": "#f5f5f5", // 背景颜色
      "backgroundImage": "",  // 背景图片，背景图片和背景颜色同时配置，图片会覆盖颜色
      "theme": { // 主题配置，对各种颜色进行自定义
        "primaryColor": "#40a9ff",
        "infoColor": "#13c2c2",
        "successColor": "#95de64",
        "processingColor": "#40a9ff",
        "errorColor": "#f5222d",
        "warningColor": "#fa8c16"
      },
      "colors": {
        "headerColor": "#ffffff", // 头部背景颜色
        "contentBgColor": "#ffffff", // pc中间方框和移动端的背景颜色
        "pcBgColor": "#f5f5f5", // pc端背景颜色
        "pcBgImage": "" // pc端背景图片
      },
      "authPage": { // 认证页面的配置
        "browserTitle": "wenet无线认证", // 浏览器tab页标题
        "title": "连接上网", // 内容中的标题
        "authForm": [ // 认证表单配置，数组类型
          {
            "name": "webAuthUser", // 表单的name
            "placeholder": "用户名", // 输入框为空时的提示文字
            "type": "input", // 表单类型: 有input, select, password, 如果是select, 还需要配置options或 (useRemoteOptions: true + remoteOptionsConfig)
            // options: {
            //   // 下拉框的内容
            //   label: string;
            //   // 下拉框的值
            //   value: string; // 在作为前缀或者后缀时,如果希望用户可以选择该选项,但实际不拼接前缀后缀,可以把value填写为一个带# 的字符串, 如 [{ label: '选项一', value: '#1'}, { label: '选项二', value: '#2'}]
            // }[];
            searchable: true, // 是否可搜索
            useRemoteOptions: true, // 使用远程数据
            remoteOptionsConfig: { // 获取到远程数据后,填写到下拉框的配置
              listPath: [], // 从远程数据获取列表的路径, 如果远程数据的结构为 { data: { list: [] }}, 则填写['data', 'list']
              url: string, // 远程数据的地址
              labelName: string,
              valueName: string,
              uniqueKey: string, // 列表中,值唯一的字段,如id,ou
            },
            /**
             * 前缀类型：取值 'fixed' 、'form'、'none'
             * fixed: 固定值，直接使用prefixValue的值
             * form：取name为prefixValue的form的value
             * none: 无前缀
             */
            "prefixType": "none",
            /**
             * 后缀类型：取值 'fixed' 、'form'、'none'
             * fixed: 固定值，直接使用prefixValue的值
             * form：取name为prefixValue的form的value
             * none: 无前缀
             */
            "suffixType": "none",
            // 后缀值
            "prefixValue": "",
            // 前缀值
            "suffixValue": "",
            // 是否必填
            "required": true,
            // 是否携带到后端
            "isCarry": true,
            // 输入为空时的错误提示
            "emptyTip": "请输入用户名",
            // 是否可以被记住
            "rememberable": true,
            // 图标
            "icon": "UserOutlined"
          },
          {
            "name": "webAuthPassword",
            "placeholder": "密码",
            "type": "password",
            "required": true,
            "isCarry": true,
            "emptyTip": "请输入密码",
            "rememberable": true,
            "icon": "KeyOutlined"
          }
        ],
        "authButtonText": "连接", // 认证按钮的文字
        "rememberMeText": "记住密码", // 记住密码的文字
        "bottomLinks": [], // 下方的链接， 数组类型, 由{ "label": "string", "link": "string" } 组成
        "hidden7MoorCsBtn": true,// 是否隐藏登录页客服按钮，不填默认显示，true为隐藏
        "modal": false, // 见下方Modal类型
        "concurrentModal": { // 超并发弹窗
          "title": "连接失败，超出并发", // 标题
          "displaySessionInfo": ["macAddress", "deviceType"], // 如何展示设备
          "showDeviceName": { // 设备列表显示的设备名称
            "pc": "电脑",
            "mobile": "手机",
            "pad": "手机",
            "iphone": "手机",
            "android": "手机",
            "ios": "手机"
          }
        },
        actions: {
          noBillingPlan: "topError", // topError, arrearagePage
        },
        "messages": {// 各种提示配置
          "redirectError": "重定向参数错误，请在页面刷新后重试",
          "noPlanBind": "当前套餐未绑定手机号，前往【wenet服务中心-我的网络-我的套餐-绑定运营商账号】绑定手机号",
          "beLimited": "当前时间，网络访问被限制",
          "expired": "当前套餐已过期",
          "frozen": "当前账号被冻结，请联系客服",
          "overConcurrency": "已有设备在线",
          "invalidUsernameOrPassword": "你输入的账号或密码不正确",
          "onlyOfficeZone": "该账号仅允许在办公区域使用",
          "noBillingPlan": "未购买套餐"
        },
        // 认证方式，目前有3种type, username：用户名密码认证、wechat-mp：微信小程序扫码认证、sms-code手机号码+短信认证
        "authWays": [
          { "label": "账号密码", "type": "username" },
          { "label": "微信扫码", "type": "wechat-mp", disableAtMobile: true },
          {
            "label": "短信验证",
            "mobileInputPlaceholder": "手机号码",
            "sendSmsCodeButtonText": "发送验证码",
            "smsCodeInputPlaceholder": "输入验证码",
            "smsCodeInputEmptyTip": "验证码不能为空",
            "smsCodeInputErrorTip": "验证码错误",
            "loginButtonText": "登录",
          }
        ]
      }, // 成功页的配置
      "successPage": {
        "browserTitle": "连接成功,关注公众号查询更多信息",
        "title": "连接成功",
        "showPlan": true, // 展示账期信息
        "links": [], // 中间的链接
        "successBanner": "<div class=\"media-wrap image-wrap align-center\" style=\"text-align:center\"><img style=\"width:300px;height:300px\" class=\"media-wrap image-wrap align-center\" src=\"http://*************:7001/public/img/upload/QRcode_1651111145971.png\"/></div>", // 成功页的自定义内容，支持html
        "showDisconnect": true, // 是否展示断开连接按钮
        "disconnectButtonText": "断开连接", // 断开连接按钮的文字
        "disconnectWhoAreYouModal": { // 点击断开连接，如果没有身份信息，让用户输入身份的弹窗配置
          "title": "身份验证",
          "tip": "提示提示提示提示提示提示提示提示", // 弹窗中间的提示文字
          "authForm": [ // 身份认证表单，和上面认证的一模一样
            {
              "name": "webAuthUser",
              "placeholder": "用户名",
              "type": "input",
              "prefixType": "none",
              "suffixType": "none",
              "prefixValue": "",
              "suffixValue": "",
              "required": true,
              "isCarry": true,
              "emptyTip": "请输入用户名",
              "rememberable": true,
              "icon": "UserOutlined"
            },
            {
              "name": "webAuthPassword",
              "placeholder": "密码",
              "type": "password",
              "required": true,
              "isCarry": true,
              "emptyTip": "请输入密码",
              "rememberable": true,
              "icon": "KeyOutlined"
            }
          ],
          "cancelButtonText": "取消",
          "okButtonText": "确认并断开",
          // 校验身份的方式，目前有2种type, username：用户名密码认证、、sms-code手机号码+短信认证
          "authWays": [
            { "label": "账号密码", "type": "username" },
            {
              "label": "短信验证",
              "mobileInputPlaceholder": "手机号码",
              "sendSmsCodeButtonText": "发送验证码",
              "smsCodeInputPlaceholder": "输入验证码",
              "smsCodeInputEmptyTip": "验证码不能为空",
              "smsCodeInputErrorTip": "验证码错误",
              "loginButtonText": "登录",
            }
          ]
        },
        "disconnectConfirmModal": { // 点击断开连接，如果有身份信息，给用户的提示弹窗配置
          "title": "确认断开？",
          "tip": "确认断开当前设备网络吗",
          "cancelButtonText": "取消",
          "okButtonText": "确认并断开",
          "messages": {
            "invalidUsernameOrPassword": "用户名或密码错误"
          }
        }
      },
      "arrearagePage": {
        "browserTitle": "连接失败，无可用套餐",
        "isCopy": true,
        "copyPath": "https://mp.wenet.com.cn/",
        "buttonText": "立即购买",
        "selfLink": "https://mp.wenet.com.cn/",
        "html": "<div style=\"margin-top: 32px;\"><div style=\"font-size: 20px; text-align: center; margin-bottom: 32px; font-weight: bold;\">你没有可用套餐，<br />关注公众号进行购买</div><p style=\"color: #9ca3af; text-align: center; margin-left: 24px; margin-right: 24px; margin-bottom: 24px;\">【温馨提示：若您已办理含校园网的新卡，请前往WeNet服务中心公众号-自助服务-激活宽带进行校园网激活】</p><div style=\"display: flex; flex: 1; align-items: center; justify-content: space-between; flex-direction: column;\"><img src=\"http://*************:6999/arrearage/QRcode.png\" alt=\"ORcode\" style=\"width: 198px; margin-bottom: 24px;\"></div></div>",
        "markUpHtml": "<div style=\"margin-top: 32px;\"><div style=\"font-size: 20px; text-align: center; margin-bottom: 64px; font-weight: bold;\">您的激活信息已失效，<br />请使用微信扫描下方二维码重新激活~</div><div style=\"display: flex; flex: 1; align-items: center; justify-content: center; flex-direction: column;\"><img src=\"http://*************:6999/arrearage/QRcode.png\" alt=\"ORcode\" style=\"width: 198px;\"></div></div>"
      },
      "findSuccessPage": {
        "browserTitle": "找回密码成功,关注公众号查询更多信息",
        "isCopy": true,
        "copyPath": "https://mp.wenet.com.cn/",
        "html": ""
      },
      "deviceListPage": {
        "browserTitle": "在线会话",
        "displaySessionInfo": ["macAddress", "deviceType"], // 如何展示设备
        "showReconnectButton": false, // 是否展示重新连接按钮
        "showDeviceName": { // 设备列表显示的设备名称
          "pc": "电脑",
          "mobile": "手机",
          "pad": "手机",
          "iphone": "手机",
          "android": "手机",
          "ios": "手机"
        },
        "whoAreYouModal": { // 进入在线会话列表页面，如果没有身份信息，让用户输入身份的弹窗配置
          "title": "身份验证",
          "tip": "提示提示提示提示提示提示提示提示", // 弹窗中间的提示文字
          "authForm": [ // 身份认证表单，和上面认证的一模一样
            {
              "name": "webAuthUser",
              "placeholder": "用户名",
              "type": "input",
              "prefixType": "none",
              "suffixType": "none",
              "prefixValue": "",
              "suffixValue": "",
              "required": true,
              "isCarry": true,
              "emptyTip": "请输入用户名",
              "rememberable": true,
              "icon": "UserOutlined"
            },
            {
              "name": "webAuthPassword",
              "placeholder": "密码",
              "type": "password",
              "required": true,
              "isCarry": true,
              "emptyTip": "请输入密码",
              "rememberable": true,
              "icon": "KeyOutlined"
            }
          ],
          "cancelButtonText": "取消",
          "okButtonText": "确认并断开",
          // 校验身份的方式，目前有2种type, username：用户名密码认证、、sms-code手机号码+短信认证
          "authWays": [
            { "label": "账号密码", "type": "username" },
            {
              "label": "短信验证",
              "mobileInputPlaceholder": "手机号码",
              "sendSmsCodeButtonText": "发送验证码",
              "smsCodeInputPlaceholder": "输入验证码",
              "smsCodeInputEmptyTip": "验证码不能为空",
              "smsCodeInputErrorTip": "验证码错误",
              "loginButtonText": "登录",
            }
          ]
        },
      },
      "activityPages": [],
      "systemConfig": {
        "detectJsUrl": "http://oss.wenet.com.cn/captive-portal/connection-test.js", // 探测地址
        "detectTimeoutMs": "1000", // 探测超时时间 毫秒
        "autoRedirectUrl": "http://qq.com", // 自动重定向的地址
        "autoRedirecWhenNoRedirectInfo": true, // 没有重定向信息时，是否自动重定向，（部分机型不支持，浏览器会把这种行为当作流氓行为）,
        "autoRedirectDelay": 1; // 因无重定向参数导致的认证失败后的自动跳转延迟时间,单位秒
      }
    }
  ]
}

```

## 类型参考

```ts
interface SelectFormType {
  // 表单的name
  name: string;
  // 输入框为空时的提示文字
  placeholder: string;
  // 表单类型: 下拉框
  type: 'select';
  // 下拉框的选项(数组类型)
  options: {
    // 下拉框的内容
    label: string;
    // 下拉框的值
    value: string;
  }[];
  // 下拉框默认选择的值
  default?: string;
  // 是否必填
  required: boolean;
  // 是否携带到后端
  isCarry: boolean;
  // 输入为空时的错误提示
  emptyTip: string;
  // 是否可以被记住
  rememberable: boolean;
}

interface InputFormType {
  // 表单的name
  name: string;
  // 输入框为空时的提示文字
  placeholder: string;
  // 表单类型：文本输入框
  type: 'input';
  /**
   * 前缀类型：取值 'fixed' 、'form'、'none'
   * fixed: 固定值，直接使用prefixValue的值
   * form：取name为prefixValue的form的value
   * none: 无前缀
   */
  prefixType: string;
  /**
   * 后缀类型：取值 'fixed' 、'form'、'none'
   * fixed: 固定值，直接使用prefixValue的值
   * form：取name为prefixValue的form的value
   * none: 无前缀
   */
  suffixType: string;
  // 后缀值
  prefixValue: string;
  // 前缀值
  suffixValue: string;
  // 是否必填
  required: boolean;
  // 是否携带到后端
  isCarry: boolean;
  // 输入为空时的错误提示
  emptyTip: string;
  // 是否可以被记住
  rememberable: boolean;
}

interface PasswordFormType {
  // 表单的值
  name: string;
  // 为空时的提示
  placeholder: string;
  // 表单类型：密码输入框
  type: 'password';
  // 是否必填
  required: boolean;
  // 是否携带到后端
  isCarry: boolean;
  // 为空时的错误提示
  emptyTip: string;
}

type AuthForm = SelectFormType | InputFormType | PasswordFormType;
interface Modal {
  // 弹窗标题
  title: string;
  // 弹窗内容:支持html
  content: string;
  // 是否展示右上角close
  closeIcon: boolean;
  // 单次会话最大的弹窗次数
  popUpCountEverySession: number;
}

declare interface PortalWebConfigration {
  // 学校名
  schoolName: string;
  // 学校的英文名(通知服务使用)
  schoolNameEn: string;
  // 学号前缀（与bas保持一致）
  telexNumber: boolean;
  // 浏览器tab页的图标
  favicon: string;
  // 页面左上角的logo
  logo: string;
  // logo位置
  logoPosition: 'left' | 'center' | 'right';
  // 是否把网站置灰(纪念节日、哀悼节日可开启)
  isWebsiteGray: boolean;
  // 页面下方的文字
  footer: string;
  // 主题
  theme: {
    // 主颜色: 按钮颜色、链接颜色、文本框聚焦颜色、loading颜色等
    primaryColor?: string;
    // 信息提示颜色(暂未使用)
    infoColor?: string;
    // 成功颜色：成功页对勾的icon
    successColor?: string;
    // 处理中的颜色(暂未使用)
    processingColor?: string;
    // 错误、危险颜色：断开连接按钮的颜色、错误提示的颜色
    errorColor?: string;
    // 警告色：警告弹窗的颜色
    warningColor?: string;
  };
  colors: {
    // 网页头部的背景颜色
    headerColor: string;
    // 中间块的背景色
    contentBgColor: string;
    // pc端body背景色
    pcBgColor: string;
    // pc端body背景图，背景图会覆盖背景色
    pcBgImage: string;
  };
  // 全局的浏览器tab页标题，再其他页面没有配置的时候会显示
  browserTitle: string;
  // 认证页面
  authPage: {
    // 浏览器tab页标题
    browserTitle: string;
    // 内容中的标题
    title: string;
    // 认证表单配置，数组类型，见AuthForm
    authForm: AuthForm[];
    // 认证按钮的文字
    authButtonText: string;
    // 记住密码的文字
    rememberMeText: string;
    // 下方的链接， 数组类型
    bottomLinks: {
      // 链接
      link: string;
      // 文字
      label: string;
    }[];
    // 是否隐藏登录页客服按钮，不填默认显示，true为隐藏
    hidden7MoorCsBtn: boolean;
    modal?: Modal;
  };
  // 成功页的配置
  successPage: {
    // 浏览器tab页标题
    browserTitle: string;
    // 是否查询并展示账期
    showPlan: boolean;
    // 中间的链接
    links: {
      link: string;
      label: string;
    }[];
    // 成功页的自定义内容，支持html
    successBanner: string;
    // 是否展示断开连接按钮
    showDisconnect: boolean;
    // 断开连接按钮的问题
    disconnectButtonText: string;
    // 成功页的弹窗：见Modal配置
    modal: Modal;
    redetectAfterDisconnectMs: number; // 断开连接后延迟探测的时间单位ms
  };
  // 引导购买套餐页
  arrearagePage: {
    browserTitle: string;
    // 内容，支持html
    html: string;
    // 过期套餐为加收套餐时使用markUpHtml替代html展示，支持html
    markUpHtml: string;
    // 是否展示复制公众号
    isCopy: boolean;
    // 复制的内容
    copyText?: string;
    // 下方按钮的文字
    buttonText: string;
    // 跳转按钮的跳转链接
    selfLink: string;
  };
  // v找回密码成功页
  findSuccessPage: {
    browserTitle: string;
    // 内容，支持html
    html: string;
    // 是否展示复制公众号
    isCopy: boolean;
    // 复制的内容
    copyText: string;
  };
  concurrentPage: {
    browserTitle: string;
    // 展示的在线会话类型,deviceType展示设备类型(pc、iphone、ipad、android)，macAddress展示mac地址
    displaySessionInfoType: 'deviceType' | 'macAddress';
  };
  deviceListPage: {
    browserTitle: string;
    // 展示的在线会话类型,deviceType展示设备类型(pc、iphone、ipad、android)，macAddress展示mac地址
    displaySessionInfoType: 'deviceType' | 'macAddress';
  };
  // 活动页（可配置无限多的页面），可以在successPage.links或authPage.links配置，格式为/activity/:id，
  activityPages: {
    // 活动id(唯一)
    id: number;
    // 活动页的浏览器tab标题
    browserTitle: string;
    // 活动页的内容
    html: string;
  }[];
  // 系统配置
  systemConfig: {
    // js探测地址
    detectJsUrl: string;
    // js探测超时时间，默认2000ms
    detectTimeoutMs: number;
    // 重定向参数丢失时，自动重定向的地址
    autoRedirectUrl: string;
  };
}
```
