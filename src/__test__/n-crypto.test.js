import { decrypt, encrypt } from '../utils/n-crypto';


describe('测试加密解密', () => {
  test('解密后的和加密前的字符串相同', () => {
    const str = 'nroad';
    const enc = encrypt(str);
    let dec = '';
    if (enc) {
      dec = decrypt(enc);
    }
    expect(str === dec).toBeTruthy()
  })
  
  test('解密后的对象和加密前的对象相同', () => {
    const obj = {
      a: 1, 
      b: {
        c: 2,
        d: 2
      }
    };
    const enc = encrypt(obj);
    let dec = '';
    if (enc) {
      dec = decrypt(enc)
    };
    const objJson = JSON.stringify(obj);
    expect(objJson === dec).toBeTruthy()
  })
  
  test('解密后的对象和加密前的数组相同', () => {
    const arr = [2, 3, 4, '112', 3, 'jsaklf']
    const enc = encrypt(arr);
    let dec = '';
    if (enc) {
      dec = decrypt(enc)
    };
    const arrJson = JSON.stringify(arr);
    expect(arrJson === dec).toBeTruthy()
  })
  
  test('解密后的对象和加密前的复杂对象相同', () => {
    const obj = {"content":[{"uid":"f2577412-48ac-4e43-a3c6-c991fdfc46e1","username":"man","orgName":"新路网络","mobile":"15667070353","ou":"10000","openAt":"2021-02-24T10:17:43.782581+08:00"}],"last":true,"totalPages":1,"totalElements":1,"sort":[{"direction":"DESC","property":"registerAt","ignoreCase":false,"nullHandling":"NATIVE","ascending":false,"descending":true}],"size":10,"number":0,"numberOfElements":1,"first":true}
    const enc = encrypt(obj);
    let dec = '';
    if (enc) {
      dec = decrypt(enc)
    };
    const objJson = JSON.stringify(obj);
    expect(objJson === dec).toBeTruthy()
  })


})
