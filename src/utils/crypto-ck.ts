import jsCookie, { <PERSON><PERSON><PERSON>ttributes } from 'js-cookie';
import { encrypt, decrypt } from '@/utils/n-crypto';

const set = (key: string, value: string, options?: CookieAttributes) => {
  const encryptKey = encrypt(key);
  const encryptValue = encrypt(value);
  if (encryptKey && encryptValue) {
    jsCookie.set(encryptKey, encryptValue, options);
  }
};

const get = (key: string) => {
  const encryptKey = encrypt(key);
  let encryptValue = '';
  if (encryptKey) {
    encryptValue = jsCookie.get(encryptKey) || '';
  }
  const res = decrypt(encryptValue) || '';
  return res;
};

const remove = (key: string) => {
  const encryptKey = encrypt(key);
  if (encryptKey) {
    jsCookie.remove(encrypt<PERSON>ey);
  }
};

export default {
  set,
  get,
  remove,
};
