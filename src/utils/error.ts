const errorCode: {
  [key: string | number]: string;
} = {
  1: ' PWS无法启',
  2: ' PWS 无法建立监听',
  3: '无法建立绑定',
  4: 'PWS 无法加载数据库',
  5: 'PWS 数据库不匹配',
  11: '服务器api已升级，旧版本需要升级后使用',
  12: '服务器正在维护，服务不可用，稍候再试',
  13: '未被处理的服务器内部错误',
  14: 'Servers are up, but overloaded with requests. Try again later.',
  21: '无效的PWS配置（pws.properties）',
  22: '其他的pws配置错误，尚未编写相关文档',
  23: '其他的pws配置错误，尚未编写相关文档',
  24: '其他的pws配置错误，尚未编写相关文档',
  25: '其他的pws配置错误，尚未编写相关文档',
  26: '其他的pws配置错误，尚未编写相关文档',
  27: '其他的pws配置错误，尚未编写相关文档',
  28: '其他的pws配置错误，尚未编写相关文档',
  29: '其他的pws配置错误，尚未编写相关文档',
  30: '其他的pws配置错误，尚未编写相关文档',
  31: '无效的NAS 配置',
  32: '未能找到对应的NAS设备',
  33: '其他的NAS配置错误，尚未编写相关文档',
  34: '其他的pws配置错误，尚未编写相关文档',
  35: '其他的NAS配置错误，尚未编写相关文档',
  36: '其他的NAS配置错误，尚未编写相关文档',
  37: '其他的NAS配置错误，尚未编写相关文档',
  38: '其他的NAS配置错误，尚未编写相关文档',
  39: '其他的NAS配置错误，尚未编写相关文档',
  41: '服务需要启用SSL',
  61: '重复的api注册',
  62: '重复的api endpoint',
  70: 'NAS无法到达',
  71: 'NAS没有响应',
  72: '不支持的协议',
  73: '不支持的NAS设备',
  74: '不支持的认证',
  75: '未能识别的应答',
  76: '无效的认证凭证（认证凭证数据错误）',
  82: '认证时已经在线',
  83: '认证暂时无法进行，稍候再试',
  85: '挑战被拒绝',
  86: '挑战时已经在线',
  87: '挑战暂时无法进行，稍候再试',
  88: '挑战失败',
  89: '下线被拒绝',
  90: '下线失败',
  91: '用户已经下线',
  101: '非法的客户端',
  102: '非法的客户端证书',
  103: '非法scope.',
  104: '无效的客户端授权.',
  105: '未找到challenge',
  106: '无效的challenge应答',
  107: '无效的客户端凭证',
  108: '不支持的响应类型',
  109: 'REST认证错误',
  121: '无效的客户端授权.',
  131: '无效的系统授权.',
  141: '未授权的访问.',
  142: '非法请求，缺少参数，参数重复，格式错误，内容错误',
  143: '请求中的实体无法处理',
  151: 'REST请求过快',
  201: '用户名密码错误',
  202: '账号类型错误',
  203: '账号状态错误',
  204: '账号未激活',
  205: '超过允许最大在线数',
  206: '不允许端口/终端接入',
  207: 'not_allowed  呼叫抑制',
  208: '登录请求过于频繁',
  209: '不允许在NAT环境中使用',
  210: '账号余额不足',
  211: '无剩余时长',
  212: '无剩余流量',
  231: '账号/MSID未注册或密码错',
  232: '账号状态非激活',
  233: '账号已在线',
  234: '账号不允许通过该端口或终端接入',
  235: '错误号86:呼叫抑制，帐号无法接入',
  236: '错误号88:请求消息不规范解码失败，拒绝接入',
  237: '账号不允许通过该端口或终端接入',
  238: '账号未订购业务',
  239: '登录时间间隔太短',
  240: '已超出该帐号在线用户数限制',
  241: '你的用户名不存在',
  242: '你的密码不正确',
  243: '账号状态错误，请进行充值或联系管理员',
  244: '请用绑定的帐号登录',
  245: '用户已在线',
  249: '未知错误信息',
  291: '未知的登录错误',
  292: '未知的下线错误',
  299: '未知的portal错误',
};

export const getErrorCode = (str: number | string) => errorCode[str];

export default {};
