import { AUTHORIZATION, ISP_TYPES, LS_USERNAME_FOR_CHECK } from '@/constants';
import { UAParser } from 'ua-parser-js';
import cryptoCk from './crypto-ck';
import { getPhysicalDeviceType } from './physical-device-type';
const parser = new UAParser();
export function getUaDeviceType(tabletIsPc: boolean) {
  // PC android ios
  // 获取用户代理字符串
  // 使用解析器解析用户代理字符串
  const result = parser.getResult();
  // 获取设备类型
  const deviceType = result.device.type;
  const osName = result.os.name;
  if (deviceType === 'tablet') {
    if (tabletIsPc) {
      return 'PC';
    }
    if (osName && /android|HarmonyOS/i.test(osName)) {
      return 'ANDROID';
    } else if (osName === 'iOS') {
      return 'IOS';
    }
  }
  // 根据设备类型进行判断
  if (deviceType === 'mobile' && osName) {
    // osName 包含android HarmonyOS 正则匹配，忽略大小写
    if (/android|HarmonyOS/i.test(osName)) {
      return 'ANDROID';
    } else if (osName === 'iOS') {
      return 'IOS';
    }
  }
  return 'PC';
}

export function getDeviceType({ devicetypeUaFirst = true, tabletIsPc = true }: { devicetypeUaFirst?: boolean; tabletIsPc?: boolean }) {
  if (devicetypeUaFirst) {
    return getUaDeviceType(tabletIsPc);
  }
  return getPhysicalDeviceType(tabletIsPc);
}

export function saveToken(token: string) {
  if (token) {
    localStorage.removeItem(AUTHORIZATION);
    cryptoCk.remove(AUTHORIZATION);
    localStorage.setItem(AUTHORIZATION, token);
    cryptoCk.set(AUTHORIZATION, token);
  }
}

export function getToken() {
  return localStorage.getItem(AUTHORIZATION) || cryptoCk.get(AUTHORIZATION);
}

export function getRedirectUrlVale(url: string) {
  // redirectUrl=/?usermac=50-ED-3C-53-B1-18&userip=*************&origurl=http://qq.com/&nasip=***********
  const [, s1] = url.split('?');
  let res: {
    [key: string]: string;
  } | null = null;
  if (s1) {
    res = {};
    const kvs = s1.split('&');
    kvs.forEach((kv) => {
      const [k, v] = kv.split('=');
      if (res) {
        res[k] = v;
      }
    });
  }
  return res;
}

export const usernameForCheck = {
  get() {
    return localStorage.getItem(LS_USERNAME_FOR_CHECK) || undefined;
  },
  set(value: string) {
    localStorage.setItem(LS_USERNAME_FOR_CHECK, value);
  },
  remove() {
    localStorage.removeItem(LS_USERNAME_FOR_CHECK);
  },
};

export function isMobileDevice(): boolean {
  const userAgent = navigator.userAgent;
  const mobileKeywords = ['Android', 'iPhone', 'iPad', 'iPod', 'Windows Phone'];

  return mobileKeywords.some((keyword) => userAgent.includes(keyword));
}

export function getIspName(key: string) {
  const k = key.toUpperCase();
  return ISP_TYPES[k] || key;
}

export function iosRefresh() {
  const os = parser.getOS().name;
  if (os && ['iOS', 'Mac OS'].includes(os)) {
    window.location.reload();
  }
}

// 拼接前缀或后缀
export function joinPrefixOrSuffix(
  origin: string,
  inputForm: Pick<InputFormType, 'prefixType' | 'prefixValue' | 'suffixType' | 'suffixValue'>,
  formValues: any
) {
  let value = origin;
  switch (inputForm.prefixType) {
    case 'fixed':
      value = inputForm.prefixValue + origin;
      break;
    case 'form': {
      const prefixStr = formValues[inputForm.prefixValue] || '';
      if (prefixStr.includes('#')) {
        // 包含#号，表示这个值是系统生成的，不需要拼接
        value = origin;
      } else {
        value = prefixStr + origin;
      }
      break;
    }
    case 'none':
      value = origin;
      break;
    default:
      value = origin;
      break;
  }

  switch (inputForm.suffixType) {
    case 'fixed':
      value += inputForm.suffixValue;
      break;
    case 'form': {
      const suffixStr = formValues[inputForm.suffixValue] || '';
      if (suffixStr.includes('#')) {
        // 包含#号，表示这个值是系统生成的，不需要拼接
        value = origin;
      } else {
        value += suffixStr;
      }
      break;
    }
    default:
      break;
  }
  return value;
}

export function parseUrlToObj(url: string): Record<string, any> {
  if (!url) {
    return {};
  }
  const res: Record<string, string | string[]> = {};
  const [, paramsStr] = url.split('?');
  if (paramsStr) {
    const kvArr = paramsStr.split('&');
    kvArr.forEach((kvStr) => {
      const [k, v] = kvStr.split('=');
      if (res[k]) {
        if (Array.isArray(res[k])) {
          (res[k] as string[]).push(v);
        } else {
          res[k] = [res[k], v] as string[];
        }
      } else {
        res[k] = v;
      }
    });
  }
  return res;
}

export const identityCodeValid = (code: string) => {
  let city: { [key: string]: string } = {
    11: '北京',
    12: '天津',
    13: '河北',
    14: '山西',
    15: '内蒙古',
    21: '辽宁',
    22: '吉林',
    23: '黑龙江 ',
    31: '上海',
    32: '江苏',
    33: '浙江',
    34: '安徽',
    35: '福建',
    36: '江西',
    37: '山东',
    41: '河南',
    42: '湖北 ',
    43: '湖南',
    44: '广东',
    45: '广西',
    46: '海南',
    50: '重庆',
    51: '四川',
    52: '贵州',
    53: '云南',
    54: '西藏 ',
    61: '陕西',
    62: '甘肃',
    63: '青海',
    64: '宁夏',
    65: '新疆',
    71: '台湾',
    81: '香港',
    82: '澳门',
    91: '国外 ',
  };
  let pass = true;

  if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
    pass = false;
  } else if (!city[code.substr(0, 2)]) {
    pass = false;
  } else {
    //18位身份证需要验证最后一位校验位
    if (code.length === 18) {
      //校验码判断
      let c = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; //系数
      let b = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']; //校验码对照表
      let id_array = code.split('');
      let sum = 0;
      for (let k = 0; k < 17; k++) {
        sum += parseInt(id_array[k]) * c[k];
      }
      if (id_array[17].toUpperCase() !== b[sum % 11].toUpperCase()) {
        pass = false;
      }
    }
  }
  return pass;
};

export default {};
