/**
 * 判断设备是否为 Android，包含**桌面模式**
 * @see https://useragentstring.com/ ua 检测
 * @returns
 */
class DeviceSDK {
  isWindows = () => {
    const ua = window.navigator.userAgent;
    // return /win/i.test(window.navigator.platform) || /windows/i.test(ua);
    return /windows/i.test(ua);
  };
  isMac = () => {
    const ua = window.navigator.userAgent;
    // return (/mac/i.test(window.navigator.platform) || /mac os/i.test(ua)) && !window.navigator.maxTouchPoints;
    return /mac os/i.test(ua);
  };
  isAndroid = () => {
    const ua = window.navigator.userAgent;
    return (
      /android/i.test(ua) || // android
      /miuibrowser/i.test(ua) || // 小米浏览器
      /huawei/i.test(window.navigator.appPackageName) || // 华为浏览器
      (/linux/i.test(window.navigator.platform) && window.navigator.maxTouchPoints > 0)
    ); // android 系统平板
  };
  isIOS = () => {
    const ua = window.navigator.userAgent;
    return /(ios | iphone)/i.test(ua) || /iphone/i.test(window.navigator.platform);
  };
  isIPadOs = () => {
    const ua = window.navigator.userAgent;
    return (
      /ipad/i.test(ua) ||
      (!/chrome/i.test(ua) && /safari/i.test(ua) && !this.isIOS() && window.navigator.maxTouchPoints > 0) || // Safari 桌面模式
      (/mac/i.test(window.navigator.platform) && !this.isIOS() && window.navigator.maxTouchPoints > 0)
    ); // ipad
  };
  // 暂时不区分安卓平板和手机（需要寻找区分平板和手机的方法，小米不兼容以下方法）
  // isAndroidPad = () => {
  //   const ua = window.navigator.userAgent;
  //   return this.isAndroid() && !(/mobile/i.test(ua))
  // };
  // isAndroidPhone = () => {
  //   const ua = window.navigator.userAgent;
  //   return this.isAndroid() && /mobile/i.test(ua)
  // };
}
const DVJS = new DeviceSDK();

export const getOsType = () => {
  if (DVJS.isAndroid()) {
    return 'android';
  }
  if (DVJS.isIPadOs()) {
    return 'iPadOs';
  }
  if (DVJS.isIOS()) {
    return 'ios';
  }
  if (DVJS.isWindows()) {
    return 'windows';
  }
  if (DVJS.isMac()) {
    return 'mac';
  }
  return 'unknown';
};

export function getPhysicalDeviceType(tabletIsPc: boolean) {
  const physicalDeviceType = getOsType();
  let type;

  if (['windows', 'mac'].includes(physicalDeviceType)) {
    type = 'PC';
  } else if (physicalDeviceType === 'android') {
    type = 'ANDROID';
  } else if (['iPadOs'].includes(physicalDeviceType)) {
    return tabletIsPc ? 'PC' : 'IOS';
  } else if (['ios'].includes(physicalDeviceType)) {
    type = 'IOS';
  } else {
    type = 'PC';
  }
  return type;
}

export const getNavigatorObjString = () => {
  const n = window.navigator;
  const obj: any = {};
  for (let key in n) {
    const value = window.navigator[key];
    if (['number', 'string'].includes(typeof value)) {
      obj[key] = value;
    } else {
      obj[key] = JSON.stringify(value);
    }
  }
  return JSON.stringify(obj);
};

export default DVJS;
