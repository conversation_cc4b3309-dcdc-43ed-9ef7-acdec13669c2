/*
 * @Author: yuy<PERSON>
 * @Date: 2021-04-29 12:45:33
 * @LastEditTime: 2021-04-29 14:23:28
 * @LastEditors: yuyang
 */
import CryptoJS from 'crypto-js';

export class CryptoLs {
  autoEncrypt: boolean;

  aesKey: string;

  constructor() {
    this.autoEncrypt = true;
    this.aesKey = 'xinli8769955';
  }

  setItem(key: string, value: string) {
    let v = value;
    if (this.autoEncrypt) {
      v = this.AesEncrypt(v);
    }
    localStorage.setItem(key, v);
  }

  getItem(key: string) {
    const v = localStorage.getItem(key);
    if (v) {
      if (this.autoEncrypt) {
        return this.AesDecode(v);
      }
      return v;
    }
    return '';
  }

  /*
        AES 加密
    */
  AesEncrypt(v: string) {
    const secret = CryptoJS.AES.encrypt(v, CryptoJS.enc.Utf8.parse(this.aesKey), {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }).toString();
    return secret;
  }

  /*
        AES 解密
    */
  AesDecode(v: string) {
    let replaceSpaceStr = '';
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < v.length; i++) {
      const ch = v[i];
      if (ch === ' ') {
        replaceSpaceStr += '+';
      } else {
        replaceSpaceStr += ch;
      }
    }
    const str = CryptoJS.AES.decrypt(replaceSpaceStr, CryptoJS.enc.Utf8.parse(this.aesKey), {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }).toString(CryptoJS.enc.Utf8);
    return str;
  }
}

export default {};
