import { getLocalPortalWebConfigration, getPortalWebConfigration } from '@/api/api';

const umiEnv = process.env.UMI_ENV as 'local' | 'prod';

function getCurrentSchoolConfigration(cfg: FullConfig) {
  if (!cfg) {
    return undefined;
  }
  const schoolName = location.pathname.split('/')[1] || cfg.defaultSchoolName || 'default';
  const cfgList = cfg.configrations || [];
  const target = cfgList.find((item) => item.schoolNameEn === schoolName);
  return { target, schoolName };
}

export async function getInitialState() {
  let currentSchoolConfigration: PortalWebConfigration | undefined = undefined;
  let schoolName = '';
  try {
    if (umiEnv === 'local') {
      throw new Error('ignore me please!'); // 直接抛出异常，走catch，获取本地配置
    }
    const remoteData = await getPortalWebConfigration();
    if (remoteData) {
      const fullConfig = JSON.parse(remoteData) as FullConfig;
      const res = getCurrentSchoolConfigration(fullConfig);
      if (res) {
        currentSchoolConfigration = res.target;
        schoolName = res.schoolName;
      }
    } else {
      throw new Error('ignore me please!');
    }
  } catch (error) {
    const localData = await getLocalPortalWebConfigration();
    const res = getCurrentSchoolConfigration(localData.data);
    if (res) {
      currentSchoolConfigration = res.target;
      schoolName = res.schoolName;
    }
  }
  return {
    ...(currentSchoolConfigration as PortalWebConfigration),
    defaultSchoolName: schoolName,
  };
}

console.log('welcome portal');

export const reactQuery = {
  devtool: {
    initialIsOpen: false,
  },
  queryClient: {
    defaultOptions: {
      queries: {
        networkMode: 'always',
      },
      mutations: {
        networkMode: 'always',
      },
    },
  },
};
