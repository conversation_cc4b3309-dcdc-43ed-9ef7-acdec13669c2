import useConfigration from '@/hooks/useConfigration';
import React from 'react';

interface ContactOfficalProps {
  className?: string;
}
const ContactOffical: React.FC<ContactOfficalProps> = (props) => {
  const { className = '' } = props;
  const c = useConfigration();
  const layoutConfig = c?.layout;
  const showContact = layoutConfig?.showContact;
  const contactConfig = layoutConfig?.contact;
  if (!showContact || !contactConfig) {
    return null;
  }
  const { content, imgDescription, imgUrl } = contactConfig;
  return (
    <div className={`px-6 md:px-0 ${className}`}>
      <div className={`flex items-center gap-x-2`}>
        <div className="flex flex-col items-center gap-y-1 w-[105px] relative">
          <img src={imgUrl} alt="login-QR-code" className="w-[105px] h-[105px]"></img>
        </div>
        <div className="flex flex-col gap-y-1 items-center">
          <div className={`font-medium flex flex-col  h-full text-sm md:text-lg`}>
            <span className="inline md:block">{content}</span>
          </div>
        </div>
      </div>
      <span className="text-xs w-[105px] block text-center mt-1">{imgDescription}</span>
    </div>
  );
};

export default ContactOffical;
