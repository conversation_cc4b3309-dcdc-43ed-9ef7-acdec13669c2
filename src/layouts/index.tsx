import Fail from '@/components/Fail';
import useConfigration from '@/hooks/useConfigration';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useUrlSchool from '@/hooks/useUrlSchool';
import { Helmet, history, Link, Outlet, useLocation } from '@umijs/max';
import { ConfigProvider } from 'antd';
import 'antd/dist/antd.variable.min.css';
import jsCookie from 'js-cookie';
import { get } from 'lodash';
import React, { useEffect } from 'react';
import ContactOffical from './ContactOffical';
import './index.less';

const logoPositionMap = {
  left: 'logo_left',
  center: 'logo_center',
  right: 'logo_right',
};

const Layout: React.FC = () => {
  const configrations = useConfigration();
  const [, setRedirectUrl] = useRedirectUrl();
  const school = useUrlSchool();
  const location = useLocation();
  const pathname = location.pathname;

  ConfigProvider.config({
    theme: configrations?.theme,
  });

  const popUpCountEverySession = get(configrations, 'authPage.modal.popUpCountEverySession', 4);
  const pcBgImage = get(configrations, 'colors.pcBgImage', '/background/background.png') || '/background/background.png';
  const pcBgBlur = get(configrations, 'colors.pcBgBlur', false);

  const initBodyStyle = () => {
    const pcBgColor = get(configrations, 'colors.pcBgColor', '');
    const isWebsiteGray = get(configrations, 'isWebsiteGray', '');

    let cssText = isWebsiteGray ? 'background-color: #ffffff;' : `background-color: ${pcBgColor};`;
    document.body.style.cssText = cssText;
    document.documentElement.className = isWebsiteGray ? 'website-gray' : '';
  };

  const setModalSession = () => {
    const currentPopUpCount = 0;
    jsCookie.set('currentPopUpCount', currentPopUpCount.toString());
    jsCookie.set('popUpCountEverySession', popUpCountEverySession.toString());
  };

  useEffect(() => {
    setModalSession();
    initBodyStyle();
    setRedirectUrl();
  }, []);

  const headerColor = get(configrations, 'colors.headerColor', '');

  if (configrations) {
    const { logo, schoolName, browserTitle, favicon, layout, logoPosition = 'left' } = configrations;
    const footer = layout?.footer;
    return (
      <>
        <Helmet>
          <title>{browserTitle}</title>
          <link rel="icon" type="image/x-icon" href={favicon || '/favicon.ico'} />
        </Helmet>
        <ConfigProvider>
          <section className="layout relative box-border bg-no-repeat bg-cover flex flex-col min-h-screen md:min-h-[calc(100vh-4rem)]">
            {/* header */}
            <div className="hidden md:block md:fixed w-full md:top-0 bg-white z-20">
              <header
                className={`h-16 shadow-none text-center px-0 py-2 flex items-center box-border justify-center md:shadow-lg md:px-8 lg:px-16 ${logoPositionMap[logoPosition]}`}
                style={{
                  backgroundColor: headerColor,
                }}
              >
                <div className="block cursor-pointer" style={{ width: '257px', height: '45px' }}>
                  <img src={logo} alt={schoolName} className="object-contain h-full" onClick={() => history.replace(`/${school}/success`)} />
                </div>
              </header>
            </div>

            {/* content */}
            <div className="flex-1 flex w-full mt-[72px] md:mt-[142px]">
              <div className="w-full items-start md:mt-0 md:container md:mx-auto md:w-[1107px] md:max-h-[600px] flex justify-center">
                <div id="authForm" className="w-full mx-6 md:mx-0 shadow-2xl relative rounded-3xl overflow-hidden">
                  <div
                    style={{ backgroundImage: 'linear-gradient(180deg, #FFFFFF 0%, #EEFAFF 0%, #FFFFFF 100%)' }}
                    className="absolute top-0 left-0 w-full h-full z-10 opacity-95"
                  ></div>
                  <div className="md:min-h-[480px] p-0 md:p-8 rounded-2xl flex justify-start items-start relative z-20">
                    <div className="hidden md:flex md:min-h-[480px] border-0 border-r flex-1 border-solid border-gray-200 mr-8 flex-col">
                      <div className="w-full flex-1">
                        <img src="/background/login-bg01.png" alt="login-bg01" className="w-full object-cover object-center" />
                      </div>
                      <ContactOffical />
                    </div>
                    <div className="w-full md:w-[350px] md:max-h-[480px] overflow-auto ">
                      <Outlet />
                    </div>
                  </div>
                </div>
              </div>

              {/* Background */}
              <div
                className={`fixed top-0 left-0 right-0 bottom-0 -z-20 ${pcBgBlur ? 'backdrop-blur-sm' : ''}`}
                style={{
                  backgroundImage: `url(${pcBgImage})`,
                  backgroundPosition: 'center',
                  backgroundSize: 'cover',
                  backgroundRepeat: 'no-repeat',
                }}
              />
            </div>

            {/* footer */}
            <footer id="footer-contact" className="w-full py-4 flex flex-col gap-y-2 justify-center items-center text-white bg-transparent sticky bottom-0">
              <div className="block md:hidden">
                <ContactOffical />
              </div>
              <div className="flex text-white items-center">
                {footer?.map((item, index) => (
                  <React.Fragment key={item.link}>
                    <div className="w-fit px-4 ">
                      <Link to={item.link} className="text-white">
                        {item.label}
                      </Link>
                    </div>
                    {index !== footer.length - 1 && <span className="text-white">|</span>}
                  </React.Fragment>
                ))}
              </div>
            </footer>
          </section>
        </ConfigProvider>
      </>
    );
  }

  return <Fail />;
};

export default Layout;
