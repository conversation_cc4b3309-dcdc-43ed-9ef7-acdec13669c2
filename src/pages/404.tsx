import styles from '@/components/Fail/index.less';
import { useNavigate } from '@umijs/max';
import { Button } from 'antd';

const NoFound = () => {
  const navigate = useNavigate();
  return (
    <div className={styles.Fail}>
      <div className={styles.top}>
        <p className={styles.number}>4</p>
        {/* <div>
          <img src="/favicon.ico" alt="404" className={styles.img} />
        </div> */}
        <p className={styles.number}>0</p>
        <p className={styles.number}>4</p>
      </div>
      <div className={styles.bottom}>
        <p style={{ fontSize: 16 }}>您正在寻找的页面不存在</p>
        <Button type="primary" onClick={() => navigate(-1)}>
          go back
        </Button>
        <p style={{ fontSize: 12, color: '#acada4', marginTop: 16 }}>点击返回上一页面</p>
      </div>
    </div>
  );
};

export default NoFound;
