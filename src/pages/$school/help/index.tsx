import { getNotification } from '@/api/notification';
import Back from '@/components/Back';
import HtmlWrapper from '@/components/HtmlWrapper';
import useConfigration from '@/hooks/useConfigration';
import { useQuery } from '@umijs/max';
import { Spin } from 'antd';
import { get } from 'lodash';

const Notify = () => {
  const configrations = useConfigration();
  const { isLoading, data } = useQuery(['getNotification-tongzhi'], () => {
    if (configrations) {
      const { schoolNameEn } = configrations;
      return getNotification(`${schoolNameEn}-tongzhi`);
    }
  });
  if (configrations) {
    return (
      <div className="w-full px-8 md:px-0 py-8 md:py-0">
        {isLoading ? (
          <div style={{ height: '60px', textAlign: 'center' }}>
            <Spin spinning={isLoading} />
          </div>
        ) : (
          <HtmlWrapper html={get(data, 'data.note', '')} />
        )}
        <Back />
      </div>
    );
  }
};
export default Notify;
