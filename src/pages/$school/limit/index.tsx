import Back from '@/components/Back';
import { Helmet } from '@umijs/max';
import { Col, Row } from 'antd';
import styles from './index.less';

const Limit = () => (
  <Row justify="center" className="w-full px-8 md:px-0 py-8 md:py-0">
    <Col xs={24} sm={16} md={24} className={styles.text}>
      <Helmet>
        <title>在线设备数上限的说明</title>
      </Helmet>
      <p className={styles.title} style={{ fontWeight: 'bold' }}>
        在线设备数上限的说明
      </p>
      <ol>
        <li>
          <p>什么是“在线设备”？</p>
          <p>一个上网账号的“在线设备”是指该上网账号下正在连接上网的设备，例如正在上网的手机、电脑、平板。</p>
        </li>
        <li>
          <p>什么是“在线设备数上限”？</p>
          <p>在线设备数上限”是指用户当前办理的网络套餐中所允许的在线设备数量的最大值。</p>
        </li>
        <li>
          <p>连接网络时显示“在线设备数已达上限”时该怎么办？</p>
          <p>若显示“在线设备数已达上限”则当前想要连接上网的设备无法成功连接，在选择暂时不用的在线设备进行下线操作后再进行连接网络操作。</p>
        </li>
      </ol>
      <Back />
    </Col>
  </Row>
);

export default Limit;
