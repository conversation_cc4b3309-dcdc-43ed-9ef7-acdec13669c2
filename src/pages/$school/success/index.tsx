import { deleteMac, updateRealname } from '@/api/portal';
import { UpdateRealnameParams } from '@/api/portalData';
import { disconnect } from '@/api/radacct';
import SimpleAD from '@/components/AD/SimpleAD';
import HtmlWrapper from '@/components/HtmlWrapper';
import LinkBox from '@/components/LinkBox';
import Network from '@/components/Network';
import NModal from '@/components/NModal';
import Title from '@/components/Title';
import VisibleControl from '@/components/VisibleControl';
import { useRealnamed, useUserSessionInfo } from '@/hooks/queries';
import useAD from '@/hooks/useAD';
import useConfigration from '@/hooks/useConfigration';
import useDetect from '@/hooks/useDetect';
import useQRCode from '@/hooks/useQRCode';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useUrlSchool from '@/hooks/useUrlSchool';
import useUsernameForCheck from '@/hooks/useUsernameForCheck';
import { getDeviceType, identityCodeValid } from '@/utils';
import { CheckCircleFilled } from '@ant-design/icons';
import { Helmet, useMutation, useNavigate } from '@umijs/max';
import { Button, notification } from 'antd';
import { get } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import ConfirmModal, { useConfirmModal } from './components/ConfirmModal';
import RealnameModal from './components/RealnameModal';
import WhoAreYouModal, { useWhoAreYouModal } from './components/WhoAreYouModal';
import styles from './index.less';
import Plan from './plan';
import LinkBoxItem from '@/components/LinkBox/LinkBoxItem';

const device = getDeviceType({});
const isPc = device === 'PC';

function wait(time: number) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve('');
    }, time);
  });
}

const Success: React.FC = () => {
  const configrations = useConfigration();
  const navigate = useNavigate();
  const school = useUrlSchool();
  const successPage = get(configrations, 'successPage', {});
  const successBanner = get(successPage, 'successBanner', '');
  const showPlan = get(successPage, 'showPlan', false);
  const showNetwork = get(successPage, 'showNetwork', false);
  const disconnectButtonText = get(successPage, 'disconnectButtonText');
  const redetectAfterDisconnectMs = get(successPage, 'redetectAfterDisconnectMs', 0);
  const detectDelay = get(successPage, 'detectDelayOnPageLoadMs', 0);
  const [isConnected, isDetecting, reDetect, { isPending }] = useDetect({
    immediately: true,
    detectDelay: detectDelay,
  });
  const confirmModalRef = useConfirmModal();
  const whoAreYouModalRef = useWhoAreYouModal();
  const [, , { getIp }] = useRedirectUrl();
  const userIp = getIp();
  const [username] = useUsernameForCheck();
  const [userSessionInfo] = useUserSessionInfo({ userIp, username });
  const showMarketingLandpageWhenSucess = configrations?.systemConfig?.showMarketingLandpageWhenSucess;
  const marketingPageUrl = configrations?.systemConfig?.marketingPageUrl;
  const marketingTextInPC = configrations?.systemConfig?.marketingTextInPC;
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [qrcodeRef] = useQRCode(marketingPageUrl || '');
  const [adAttrs, adContainerProps] = useAD();
  const [realnamed, { isLoading: isCheckRealnameLoading }] = useRealnamed();
  const [updateRealnameLoading, setUpdateRealnameLoading] = useState(false);
  const [updateRealnameModalOpen, setUpdateRealnameModalOpen] = useState(false);

  useEffect(() => {
    if (!realnamed) {
      setUpdateRealnameModalOpen(true);
    }
  }, [realnamed]);

  useEffect(() => {
    if (marketingPageUrl) {
      const urlObj = new URL(marketingPageUrl);
      const iframeDomain = `${urlObj.host}`;

      window.addEventListener(
        'message',
        function (event) {
          // 不信任的消息来源
          const originObj = new URL(event.origin);
          const originDomain = `${originObj.host}`;

          if (originDomain !== iframeDomain) {
            return;
          }
          if (iframeRef.current && event.data.height) {
            iframeRef.current.height = event.data.height + 'px';
          }
        },
        false
      );
    }
  }, [iframeRef.current]);

  const handleClickDisconnect = () => {
    // 如果本地缓存了用户名，或者通过ip查到了用户名，直接弹出确认框，让用户下线
    if (username || userSessionInfo?.username) {
      confirmModalRef.current?.show();
    } else {
      // 如果两个都没有，需要身份确认
      whoAreYouModalRef?.current?.show();
    }
  };

  // 断开连接
  const disconnectMutation = useMutation(
    async (uname?: string) => {
      const p = {
        username: uname || username || '',
        ...(userSessionInfo || {}),
      };

      // 优先取接口返回的ip，也就是会话源ip，如果没有，就取本地缓存重定向地址的ip
      const ip = p.framedIPAddress || userIp;
      if (ip) {
        const delay = Number(redetectAfterDisconnectMs) || 0;
        await deleteMac(p);
        await wait(1000);
        await disconnect({ userIp: ip, username: p.username });
        if (delay) {
          await wait(delay);
        }
      } else {
        return Promise.reject('参数不完整');
      }
    },
    {
      onSuccess() {
        navigate(`/${school}/auth`, { replace: true });
      },
      onError() {
        navigate(`/${school}/device-list`, { replace: true });
      },
    }
  );

  const handleSubmitRealname = (value: UpdateRealnameParams) => {
    const { idNumber } = value;
    if (!identityCodeValid(idNumber)) {
      notification.error({
        message: '身份证号不正确',
      });
      return;
    }
    setUpdateRealnameLoading(true);
    updateRealname(value)
      .then(() => {
        notification.success({
          message: '实名认证成功',
        });
      })
      .finally(() => {
        setUpdateRealnameLoading(false);
        setUpdateRealnameModalOpen(false);
      });
  };

  useEffect(() => {
    window.addEventListener('focus', reDetect, false);
    return () => {
      window.removeEventListener('focus', reDetect, false);
    };
  }, []);
  useEffect(() => {
    if (!isDetecting && !isPending && !isConnected) {
      window.location.href = `/${school}/auth`;
    }
  }, [isDetecting, isPending, isConnected]);
  if (!configrations) {
    return <>获取配置失败</>;
  }
  return (
    <div className="w-full px-8 md:px-0 py-8 md:py-0">
      <Helmet>
        <title>{configrations?.successPage?.browserTitle}</title>
      </Helmet>
      <div className="w-full flex justify-center items-center">
        <CheckCircleFilled style={{ color: '#64B85C', fontSize: 56 }} />
      </div>
      <Title className={styles.title}>{configrations?.successPage?.title}</Title>
      {adAttrs && (
        <div {...adContainerProps} className="flex justify-center items-center">
          <SimpleAD src={adAttrs?.imgUrl} position={adAttrs?.position} content={adAttrs?.content} />
        </div>
      )}
      <VisibleControl visible={!!(!isPc && showMarketingLandpageWhenSucess && marketingPageUrl)}>
        <iframe ref={iframeRef} style={{ border: 'none', width: '100%' }} src={marketingPageUrl}></iframe>
      </VisibleControl>
      {showPlan && (
        <div className="py-4 rounded-lg flex justify-center items-center">
          <div className="w-full">
            {showPlan && <Plan />}
            {showNetwork && <Network userSessionInfo={userSessionInfo} />}
          </div>
        </div>
      )}
      <VisibleControl visible={!!(showMarketingLandpageWhenSucess && marketingPageUrl)}>
        {isPc && (
          <div className="mt-4">
            <div className="text-center w-[300px] mx-auto">{marketingTextInPC}</div>
            <div className="w-[208px] h-[208px] mx-auto">
              <canvas ref={qrcodeRef}></canvas>
            </div>
          </div>
        )}
      </VisibleControl>
      <LinkBox>
        {configrations?.successPage?.links?.map((item) => (
          <LinkBoxItem key={item.link} link={item.link} blank={item.blank} bold={item.bold}>{item.label}</LinkBoxItem>
        ))}
      </LinkBox>
      <HtmlWrapper className="my-4" html={successBanner} />
      {configrations?.successPage?.showDisconnect && (
        <div className="w-full mx-auto">
          <Button type="primary" block ghost onClick={handleClickDisconnect} className={styles.disconnectButton}>
            {disconnectButtonText}
          </Button>
        </div>
      )}
      {configrations?.successPage?.modal && <NModal modal={configrations?.successPage?.modal} />}
      <VisibleControl visible={!configrations?.successPage?.disconnectWhoAreYouModal?.disable}>
        <WhoAreYouModal
          ref={whoAreYouModalRef}
          onOk={disconnectMutation.mutateAsync}
          title={configrations?.successPage?.disconnectWhoAreYouModal?.title}
          okText={configrations?.successPage?.disconnectWhoAreYouModal?.okButtonText}
          cancelText={configrations?.successPage?.disconnectWhoAreYouModal?.cancelButtonText}
          tipText={configrations?.successPage?.disconnectWhoAreYouModal?.tip}
          authWays={configrations?.successPage?.disconnectWhoAreYouModal?.authWays}
        />
      </VisibleControl>
      <ConfirmModal ref={confirmModalRef} onDisconnect={disconnectMutation.mutateAsync} />
      <RealnameModal
        open={!isCheckRealnameLoading && updateRealnameModalOpen}
        onOk={handleSubmitRealname}
        loading={updateRealnameLoading || isCheckRealnameLoading}
      />
    </div>
  );
};

export default Success;
