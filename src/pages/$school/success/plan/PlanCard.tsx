/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable react/jsx-filename-extension */
import { PlanInfo } from '@/api/portalData';
import moment from 'moment';
import React from 'react';
import styles from './index.less';

interface PlanCardProps {
  planList: PlanInfo[];
}
const PlanCard: React.FC<PlanCardProps> = (props) => {
  const { planList } = props;

  /**
   * 获取账期时间
   * @param item
   * @returns {*}
   */
  const getPlanTime = (item: PlanInfo): React.ReactNode => {
    const { expireAt } = item;
    let action;
    let day;
    // 包月的套餐
    if (expireAt) {
      const m1 = moment();
      const m2 = moment(expireAt);
      day = m2.diff(m1, 'day', true).toFixed(1);
      if (day) {
        action = (
          <span className={`${Number(day) < 5 ? styles.warning : ''}`}>
            {day}
            <span className={`${styles.des} ${Number(day) < 5 ? styles.warning : ''}`}>天</span>
          </span>
        );
      } else {
        action = (
          <span className={styles.warning}>
            小于1
            <span className={`${styles.des} ${styles.warning}`}>天</span>
          </span>
        );
      }
    }
    return action;
  };

  /**
   * 获取账期类型
   * @param item
   * @returns {*}
   */
  const getPlanType = (item: PlanInfo) => {
    let action;
    const { default_plan: defaultPlan, flowQuota, timeQuota, timeRemaining, flowRemaining } = item;
    let flowUnit = null;
    let flow = null;
    let timeUnit = '';
    let time = getPlanTime(item);
    let flowColorIsRed = false;
    let timeColorIsRed = false;
    // 生效套餐
    if (defaultPlan) {
      switch (true) {
        case flowQuota !== 0 && flowRemaining > 1000 * 1000:
          flow = (flowRemaining / 1000 / 1000).toFixed(1);
          flowUnit = 'GB';
          break;
        case flowQuota !== 0 && flowRemaining > 1000 && flowRemaining < 1000 * 1000:
          flow = (flowRemaining / 1000).toFixed(1);
          flowUnit = 'MB';
          flowColorIsRed = Number(flow) < 50;
          break;
        case flowQuota !== 0 && flowRemaining < 1000:
          flow = Math.round(flowRemaining);
          flowUnit = 'KB';
          flowColorIsRed = true;
          break;
        case timeQuota !== 0 && timeRemaining >= 36000:
          time = (timeRemaining / 60 / 60).toFixed(1);
          timeUnit = '小时';
          break;
        case timeQuota !== 0 && timeRemaining > 3600 && flowRemaining < 36000:
          time = (timeRemaining / 60 / 60).toFixed(1);
          timeUnit = '小时';
          timeColorIsRed = true;
          break;
        case timeQuota !== 0 && timeRemaining <= 3600:
          time = (timeRemaining / 60).toFixed(1);
          timeUnit = '分钟';
          timeColorIsRed = true;
          break;
        case flowQuota === 0 && timeQuota === 0:
          flow = '不限';
          time = getPlanTime(item);
          break;
        default:
          break;
      }
      // 抽取通用的
      action = (
        // <div>
        //   <div style={{ display: flow ? 'inline-block' : 'none' }}>
        //     <span className={styles.flow}>剩余流量：</span>
        //     <span className={`${styles.flowValue} ${flowColorIsRed ? styles.warning : ''}`}>
        //       {flow}
        //       <span className={`${styles.des} ${flowColorIsRed ? styles.warning : ''}`} style={{ display: flowUnit ? 'inline-block' : 'none' }}>
        //         {flowUnit}
        //       </span>
        //     </span>
        //   </div>
        //   <div>
        //     <span className={styles.flow}>剩余时长：</span>
        //     <span className={`${styles.flowValue} ${flowColorIsRed ? styles.warning : ''}`}>
        //       {time}
        //       <span className={`${styles.des} ${timeColorIsRed ? styles.warning : ''}`} style={{ display: timeUnit ? 'inline-block' : 'none' }}>
        //         {timeUnit}
        //       </span>
        //     </span>
        //   </div>
        // </div>
        <div className="w-full flex flex-wrap justify-between">
          <div className="inline-block pb-2" style={{ minWidth: '50%' }}>
            <span className={styles.itemLabel}>剩余流量：</span>
            <span className={styles.itemValue}>
              {flow}
              <span>{flowUnit}</span>
            </span>
          </div>
          <div className="inline-block pb-2">
            <span className={styles.itemLabel}>剩余时长：</span>
            <span className={styles.itemValue}>
              {time}
              <span>{timeUnit}</span>
            </span>
          </div>
        </div>
      );
    }
    return action;
  };

  if (planList.length === 0) {
    return <></>;
    // return <div style={{ textAlign: 'center', marginBottom: '10px' }}>暂无可用账期</div>;
  }
  return (
    <>
      {planList?.map((item) => (
        <React.Fragment key={item.sid}>{getPlanType(item)}</React.Fragment>
      ))}
    </>
  );
};

export default PlanCard;
