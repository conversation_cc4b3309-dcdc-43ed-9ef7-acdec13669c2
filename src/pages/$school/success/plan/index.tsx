import { getPlanService } from '@/api/portal';
import { PlanInfo } from '@/api/portalData';
import { useQuery } from '@umijs/max';
import { Spin } from 'antd';
import PlanCard from './PlanCard';

const Plan = () => {
  const { isLoading, data } = useQuery(
    ['getPlanService'],
    async () => {
      const res = await getPlanService();
      if (res.data && typeof res.data === 'string') {
        return JSON.parse(res.data) as PaginationResponse<PlanInfo>;
      }
      return {
        content: [],
      };
    },
    {
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      retry: false,
      cacheTime: 0,
      staleTime: 0,
    }
  );
  return (
    <>
      {isLoading ? (
        <div style={{ height: '60px', textAlign: 'center' }}>
          <Spin spinning={isLoading} />
        </div>
      ) : (
        <PlanCard planList={data?.content || []} />
      )}
    </>
  );
};

export default Plan;
