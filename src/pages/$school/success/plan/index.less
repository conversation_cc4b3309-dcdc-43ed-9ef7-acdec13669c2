.flow {
  opacity: 0.7;
  font-family: @font-Regular;
  font-size: 14px;
  margin-top: 12px;
}

.des {
  padding-left: 4px;
  opacity: 0.9;
  font-family: @font-Regular;
  font-size: 14px;
}

.flowValue {
  opacity: 0.9;
  font-family: @font-Medium;
  font-size: 20px;
}
.warning {
  color: @error-color;
}

.title {
  opacity: 0.5;
  font-family: @font-Regular;
  font-size: 14px;
  display: flex;
  border-bottom: 1px solid @border-color-base;
  padding-bottom: 16px;
  span {
    width: 272px;
  }
}

.values {
  opacity: 0.7;
  font-family: @font-Medium;
  font-size: 14px;
  display: flex;
  padding-top: 20px;
  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 272px;
    font-family: @font-Regular;
    font-size: 14px;
    p {
      opacity: 0.7;
      font-family: @font-Regular;
      font-size: 14px;
    }
  }
  .productName {
    font-family: @font-Medium;
    font-size: 16px;
    padding-bottom: 8px;
    font-weight: bold;
  }
}

.mobile {
  display: none;
}

.infoTitle {
  opacity: 0.9;
  font-family: @font-Regular;
  font-size: 18px;
  margin-bottom: 12px;
  text-align: center;
}

.info {
  opacity: 0.7;
  font-family: @font-Regular;
  font-size: 15px;
}

.itemLabel {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #666666;
  font-weight: 400;
}

.itemValue {
  font-size: 18px;
  color: #2d2d2d;
  font-weight: 700;
}
