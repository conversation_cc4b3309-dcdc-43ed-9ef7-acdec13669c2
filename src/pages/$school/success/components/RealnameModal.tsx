import { UpdateRealnameParams } from '@/api/portalData';
import { Button, Form, Input, Modal } from 'antd';
import React from 'react';

interface RealnameModalProps {
  open: boolean;
  onOk: (value: UpdateRealnameParams) => void;
  loading: boolean;
}
const RealnameModal: React.FC<RealnameModalProps> = (props) => {
  const [form] = Form.useForm();
  return (
    <Modal open={props.open} title="温馨提示" destroyOnClose maskClosable={false} closable={false} footer={null} centered>
      <div>
        <p className="font-bold">
          为配合国家法律法规并保障您的正常使用，请您尽快完善实名信息。如未及时完成，可能会在
          <span className="text-red-500">5分钟</span>
          后影响网络连接，感谢您的理解与支持！
        </p>
      </div>
      <Form form={form} onFinish={props.onOk} layout="vertical">
        <Form.Item label="姓名" name="realName" rules={[{ required: true, message: '请输入姓名' }]}>
          <Input placeholder="请输入姓名" />
        </Form.Item>
        <Form.Item label="身份证号" name="idNumber" rules={[{ required: true, message: '请输入身份证号' }]}>
          <Input placeholder="请输入身份证号" />
        </Form.Item>
        <Button htmlType="submit" type="primary" block loading={props.loading}>
          提交
        </Button>
      </Form>
    </Modal>
  );
};

export default RealnameModal;
