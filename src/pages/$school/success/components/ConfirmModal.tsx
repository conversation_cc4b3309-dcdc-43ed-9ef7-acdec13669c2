import TipWithIcon from '@/components/TipWithIcon';
import useConfigration from '@/hooks/useConfigration';
import { QuestionCircleFilled } from '@ant-design/icons';
import { Modal } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';

interface ConfirmModalProps {
  onShow?: () => void;
  onClose?: () => void;
  isUpdate?: boolean;
  onDisconnect?: (uname?: string) => Promise<any>;
}

type RefType = {
  show: () => void;
};

export function useConfirmModal() {
  const ref = useRef<RefType>({
    show: () => {},
  });
  return ref;
}

const ConfirmModal = React.forwardRef<RefType, ConfirmModalProps>((props, ref) => {
  const { onShow, onClose, onDisconnect } = props;
  const [visible, toggleVisible] = useState<boolean>(false);
  const configrations = useConfigration();
  const [okLoading, setOkLoading] = useState(false);

  const handleClick = () => {
    onShow?.();
    toggleVisible(true);
  };

  const handleOk = () => {
    setOkLoading(true);
    onDisconnect?.().finally(() => {
      setOkLoading(false);
    });
  };

  const handleCancel = () => {
    onClose?.();
    toggleVisible(false);
  };

  useImperativeHandle(ref, () => ({
    show: () => {
      handleClick();
    },
  }));
  return (
    <Modal
      open={visible}
      onCancel={handleCancel}
      title={configrations?.successPage?.disconnectConfirmModal?.title}
      onOk={handleOk}
      okText={configrations?.successPage?.disconnectConfirmModal?.okButtonText}
      cancelText={configrations?.successPage?.disconnectConfirmModal?.cancelButtonText}
      okButtonProps={{
        loading: okLoading,
      }}
    >
      <TipWithIcon icon={<QuestionCircleFilled />} text={configrations?.successPage?.disconnectConfirmModal?.tip} />
    </Modal>
  );
});

export default ConfirmModal;
