import { checkWhoAmI } from '@/api/portal';
import NFrom from '@/components/NForm';
import useUsernameForCheck from '@/hooks/useUsernameForCheck';
import { useMutation } from '@umijs/max';
import { Form, FormInstance, message } from 'antd';
import { AxiosError } from 'axios';
import React, { useContext, useRef } from 'react';
import whoAreYouContext from '../context';
import useConfigration from '@/hooks/useConfigration';

type AuthByUsernameProps = {
  form: FormInstance;
};

const AuthByUsername: React.FC<AuthByUsernameProps> = (props) => {
  const { form } = props;
  const { onDisconnect, setOkLoading } = useContext(whoAreYouContext);
  const configrations = useConfigration();

  const submitValuesCacheRef = useRef<any>();
  const [, setUsername] = useUsernameForCheck();

  // 验证身份
  const checkWhoAmIMutation = useMutation(checkWhoAmI, {
    onSuccess() {
      const webAuthUser: string = submitValuesCacheRef.current?.webAuthUser;
      setUsername(webAuthUser);
      onDisconnect?.(webAuthUser).finally(() => {
        setOkLoading?.(false);
      });
    },
    onError(err: AxiosError<any>) {
      setOkLoading?.(false);
      if (err.response?.data?.code === 1023) {
        message.error(configrations?.successPage?.disconnectWhoAreYouModal?.messages?.invalidUsernameOrPassword || '用户名或密码错误');
      }
    },
  });

  // 表单提交
  const finish = async (values: Record<string, string>) => {
    if (configrations) {
      const { successPage } = configrations;
      const formValue: Record<string, string> = {};
      successPage?.disconnectWhoAreYouModal?.authForm?.forEach((item) => {
        if (item.type === 'input' && item.isCarry) {
          switch (item.prefixType) {
            case 'fixed':
              formValue[item.name] = item.prefixValue + values[item.name];
              break;
            case 'form': {
              const prefixStr = values[item.prefixValue];
              if (prefixStr.includes('#')) {
                // 包含#号，表示这个值是系统生成的，不需要拼接
                formValue[item.name] = values[item.name];
              } else {
                formValue[item.name] = values[item.prefixValue] + values[item.name];
              }
              break;
            }
            case 'none':
              formValue[item.name] = values[item.name];
              break;
            default:
              formValue[item.name] = values[item.name];
              break;
          }

          switch (item.suffixType) {
            case 'fixed':
              formValue[item.name] += item.suffixValue;
              break;
            case 'form': {
              const suffixStr = values[item.suffixValue];
              if (suffixStr.includes('#')) {
                // 包含#号，表示这个值是系统生成的，不需要拼接
                formValue[item.name] += values[item.name];
              } else {
                formValue[item.name] += values[item.suffixValue];
              }
              break;
            }
            default:
              break;
          }
        }
        if (item.type === 'select' && item.isCarry) {
          formValue[item.name] = values[item.name];
        }
        if (item.type === 'password' && item.isCarry) {
          formValue[item.name] = values[item.name];
        }
      });
      const p = {
        ...formValue,
      };
      submitValuesCacheRef.current = p;
      setOkLoading?.(true);
      await checkWhoAmIMutation.mutateAsync(p);
    }
  };
  return (
    <Form form={form} onFinish={finish} layout="horizontal" validateTrigger="onBlur">
      {configrations?.successPage?.disconnectWhoAreYouModal?.authForm?.map((item) => (
        <NFrom key={item.name} authFormItem={item} />
      ))}
    </Form>
  );
};

export default AuthByUsername;
