import { checkWhoAmIBySmsCode, sendVerifyCode } from '@/api/portal';
import TimingButton from '@/components/TimingButton';
import useLocalStorage from '@/hooks/useLocalStorage';
import useUsernameForCheck from '@/hooks/useUsernameForCheck';
import regs from '@/utils/regs';
import { useMutation } from '@umijs/max';
import { Form, FormInstance, Input, message } from 'antd';
import { AxiosError } from 'axios';
import React, { useContext, useEffect, useRef } from 'react';
import whoAreYouContext from '../context';
import useConfigration from '@/hooks/useConfigration';
import NFrom from '@/components/NForm';
import { joinPrefixOrSuffix } from '@/utils';

type AuthBySmsCodeProps = {
  form: FormInstance;
  smsCodeAuthConfig: AuthBySmsCode;
};

const AuthBySmsCodeComponent: React.FC<AuthBySmsCodeProps> = (props) => {
  const { form, smsCodeAuthConfig } = props;
  const { onDisconnect, setOkLoading } = useContext(whoAreYouContext);
  const configrations = useConfigration();
  const [cachedParams, setCachedParams] = useLocalStorage<{ webAuthUser: string }>('smsCodeAuth', { webAuthUser: '' });

  const submitValuesCacheRef = useRef<any>();
  const [, setUsername] = useUsernameForCheck();

  useEffect(() => {
    if (cachedParams?.webAuthUser) {
      form.setFieldValue('webAuthUser', cachedParams.webAuthUser);
    }
  }, []);

  const webAuthUserReg = smsCodeAuthConfig?.mobileInputRegex ? new RegExp(smsCodeAuthConfig?.mobileInputRegex) : regs.phone;

  // 验证身份
  const checkWhoAmIMutation = useMutation(checkWhoAmIBySmsCode, {
    onSuccess() {
      const webAuthUser: string = submitValuesCacheRef.current?.webAuthUser;
      setUsername(webAuthUser);
      setCachedParams({ webAuthUser });
      onDisconnect?.(webAuthUser).finally(() => {
        setOkLoading?.(false);
      });
    },
    onError(err: AxiosError) {
      setOkLoading?.(false);
      if (err.response?.data?.code === 1023) {
        message.error(configrations?.successPage?.disconnectWhoAreYouModal?.messages?.invalidUsernameOrPassword || '用户名或密码错误');
      }
    },
  });

  // 表单提交
  const handleFinish = async (values: Record<string, string>) => {
    if (configrations) {
      const { verifyCode, webAuthUser } = values;
      setOkLoading?.(true);
      const fullWebAuthUser = joinPrefixOrSuffix(
        webAuthUser,
        {
          prefixType: smsCodeAuthConfig?.mobilePrefixType,
          prefixValue: smsCodeAuthConfig?.mobilePrefixValue,
          suffixType: smsCodeAuthConfig?.mobileSuffixType,
          suffixValue: smsCodeAuthConfig?.mobileSuffixValue,
        },
        values
      );
      const p = {
        webAuthUser: fullWebAuthUser,
        verifyCode,
      };
      submitValuesCacheRef.current = p;
      await checkWhoAmIMutation.mutateAsync(p);
    }
  };

  const handleClickSendSmsCode = async () => {
    const { webAuthUser } = await form.validateFields(['webAuthUser']);
    const formValues = form.getFieldsValue();
    const fullWebAuthUser = joinPrefixOrSuffix(
      webAuthUser,
      {
        prefixType: smsCodeAuthConfig?.mobilePrefixType,
        prefixValue: smsCodeAuthConfig?.mobilePrefixValue,
        suffixType: smsCodeAuthConfig?.mobileSuffixType,
        suffixValue: smsCodeAuthConfig?.mobileSuffixValue,
      },
      formValues
    );
    const res = await sendVerifyCode(fullWebAuthUser);
    if (res) {
      message.success(smsCodeAuthConfig?.smsCodeSendSuccessTip || '验证码发送成功');
      return true;
    } else {
      message.error(smsCodeAuthConfig?.smsCodeSendErrorTip || '验证码发送失败');
      return Promise.reject(new Error(''));
    }
  };

  return (
    <Form form={form} layout="horizontal" onFinish={handleFinish} size="large" validateTrigger="onBlur">
      {smsCodeAuthConfig?.prependForm?.map((item, idx) => (
        <NFrom authFormItem={item} key={idx} />
      ))}
      <Form.Item
        name="webAuthUser"
        validateFirst
        rules={[
          { required: true, message: smsCodeAuthConfig?.mobileInputEmptyTip || '请输入手机号' },
          { pattern: webAuthUserReg, message: smsCodeAuthConfig?.mobileInputErrorTip || '请输入正确的手机号' },
        ]}
      >
        <Input placeholder={smsCodeAuthConfig?.mobileInputPlaceholder} />
      </Form.Item>
      <div className="w-full flex">
        <Form.Item className="flex-1 w-12" name="verifyCode" rules={[{ required: true, message: '请输入验证码' }]}>
          <Input placeholder={smsCodeAuthConfig?.smsCodeInputPlaceholder} />
        </Form.Item>
        <TimingButton textAfterNumber="秒后可重发" min={0} max={60} onClick={handleClickSendSmsCode}>
          {smsCodeAuthConfig?.sendSmsCodeButtonText}
        </TimingButton>
      </div>
    </Form>
  );
};

export default AuthBySmsCodeComponent;
