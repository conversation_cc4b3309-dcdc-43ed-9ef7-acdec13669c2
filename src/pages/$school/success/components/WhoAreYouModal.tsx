import TipWithIcon from '@/components/TipWithIcon';
import VisibleControl from '@/components/VisibleControl';
import useConfigration from '@/hooks/useConfigration';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Form, Modal, Tabs, TabsProps } from 'antd';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import AuthBySmsCode from './auth-ways/AuthBySmsCode';
import AuthByUsername from './auth-ways/AuthByUsername';
import { Provider } from './context';

interface WhoAreYouModalProps {
  onShow?: () => void;
  onClose?: () => void;
  isUpdate?: boolean;
  onOk?: (uname?: string) => Promise<any>;
  title?: React.ReactNode;
  okText?: React.ReactNode;
  cancelText?: React.ReactNode;
  tipText?: React.ReactNode;
  authWays?: AuthWay[];
}

type RefType = {
  show: () => void;
  close: () => void;
};

export function useWhoAreYouModal() {
  const ref = useRef<RefType>({
    show: () => {},
    close: () => {},
  });
  return ref;
}

const WhoAreYouModal = React.forwardRef<RefType, WhoAreYouModalProps>((props, ref) => {
  const { onShow, onClose, onOk, title, okText, cancelText, tipText, authWays = [] } = props;
  const [visible, toggleVisible] = useState<boolean>(false);
  const configrations = useConfigration();
  const [activeKey, setActiveKey] = useState<AuthWayType>('sms-code');
  const [okLoading, setOkLoading] = useState(false);
  const [authByUsernameForm] = Form.useForm();
  const [authBySmsCodeForm] = Form.useForm();

  const smsCodeAuthConfig = useMemo(() => {
    const smsCodeAuthConfig = authWays?.find((item) => item.type === 'sms-code');
    return smsCodeAuthConfig as AuthBySmsCode;
  }, [configrations]);

  const handleChangeKey = (key: string) => {
    setActiveKey(key as AuthWayType);
  };

  const handleClick = () => {
    onShow?.();
    toggleVisible(true);
  };

  const handleOk = () => {
    if (activeKey === 'username') {
      authByUsernameForm.submit();
      return;
    } else if (activeKey === 'sms-code') {
      authBySmsCodeForm.submit();
      return;
    }
  };

  const handleCancel = () => {
    onClose?.();
    toggleVisible(false);
  };

  useImperativeHandle(ref, () => ({
    show: () => {
      handleClick();
    },
    close: () => {
      toggleVisible(false);
    },
  }));

  const tabsItem = useMemo(() => {
    const res: TabsProps['items'] = [];
    authWays?.forEach((item) => {
      res.push({
        label: item.label,
        key: item.type,
      });
    });
    return res;
  }, [authWays]);

  useEffect(() => {
    const firstActiveKey = authWays?.[0]?.type;
    if (!firstActiveKey) return;
    setActiveKey(firstActiveKey);
  }, [authWays]);

  return (
    <Modal
      open={visible}
      onCancel={handleCancel}
      title={title}
      onOk={handleOk}
      okText={okText}
      cancelText={cancelText}
      okButtonProps={{
        loading: okLoading,
      }}
    >
      <Provider value={{ onDisconnect: onOk, setOkLoading }}>
        <TipWithIcon icon={<ExclamationCircleFilled />} text={tipText} />
        <div className="h-2"></div>
        <Tabs items={tabsItem} activeKey={activeKey} onChange={handleChangeKey} />
        <div>
          {tabsItem.map((tabItem) => {
            switch (tabItem.key) {
              case 'username': {
                return (
                  <VisibleControl key={tabItem.key} visible={tabItem.key === activeKey}>
                    <AuthByUsername form={authByUsernameForm} />
                  </VisibleControl>
                );
              }
              case 'sms-code': {
                return (
                  <VisibleControl key={tabItem.key} visible={tabItem.key === activeKey}>
                    <AuthBySmsCode form={authBySmsCodeForm} smsCodeAuthConfig={smsCodeAuthConfig} />
                  </VisibleControl>
                );
              }
              default: {
                return <></>;
              }
            }
          })}
        </div>
      </Provider>
    </Modal>
  );
});

export default WhoAreYouModal;
