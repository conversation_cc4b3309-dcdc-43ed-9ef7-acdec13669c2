import { deleteMac, reConnect } from '@/api/portal';
import { disconnectDevice, getDeviceList } from '@/api/radacct';
import { SessionDevice } from '@/api/radacctData';
import Back from '@/components/Back';
import DeviceType from '@/components/DeviceType';
import Title from '@/components/Title';
import VisibleControl from '@/components/VisibleControl';
import { useUserSessionInfo } from '@/hooks/queries';
import useConfigration from '@/hooks/useConfigration';
import useDetect from '@/hooks/useDetect';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useUrlSchool from '@/hooks/useUrlSchool';
import useUsernameForCheck from '@/hooks/useUsernameForCheck';
import { ReloadOutlined } from '@ant-design/icons';
import { Helmet, Link, useMutation, useQuery } from '@umijs/max';
import { Button, List, message } from 'antd';
import { get } from 'lodash';
import { useEffect, useState } from 'react';
import WhoAreYouModal, { useWhoAreYouModal } from '../success/components/WhoAreYouModal';
import styles from './index.less';

function DeviceList() {
  const [forceWaiting, setForceWaiting] = useState(false);
  const configrations = useConfigration();
  const [, , { getIp }] = useRedirectUrl();
  const userIp = getIp();
  const school = useUrlSchool();
  const [cachedUsername, saveUsername] = useUsernameForCheck();
  const whoAreYouModalRef = useWhoAreYouModal();
  const [userSessionInfo] = useUserSessionInfo(
    { userIp, username: cachedUsername },
    {
      onSuccess(data) {
        if (!data && !cachedUsername) {
          whoAreYouModalRef.current?.show();
        }
      },
    }
  );
  const deviceListQuery = useQuery(
    ['getDeviceList', cachedUsername, userSessionInfo],
    () => {
      const ip = userSessionInfo?.framedIPAddress || userIp;
      const uname = userSessionInfo?.username || cachedUsername;
      return getDeviceList({
        userIp: ip,
        username: uname,
      });
    },
    {
      enabled: !!(cachedUsername || userSessionInfo?.username),
    }
  );

  const [isConnected, isDetecting, reDetect, { isPending }] = useDetect({
    immediately: false,
  });

  const browserTitle = get(configrations, 'deviceListPage.browserTitle', '');
  const displaySessionInfo = get(configrations, 'deviceListPage.displaySessionInfo', 'deviceType') || [];
  const showReconnectButton = get(configrations, 'deviceListPage.showReconnectButton', true);

  const disconnectMutation = useMutation(disconnectDevice, {
    onSuccess() {
      setForceWaiting(true);
      message.success('下线成功');
      reDetect();
      setTimeout(() => {
        setForceWaiting(false);
        deviceListQuery.refetch();
      }, 3000);
    },
  });

  const deleteMacMutation = useMutation(
    ['deleteMac'],
    (params: SessionDevice) => {
      return deleteMac(params);
    },
    {
      onSuccess(_, variables) {
        const { acctSessionId, nasIpAddress } = variables;
        disconnectMutation.mutate({
          sessionId: acctSessionId,
          nasIp: nasIpAddress,
          userIp: getIp(),
        });
      },
    }
  );

  const reConnectMutation = useMutation(reConnect, {
    onSuccess() {
      message.success('连接成功');
      deviceListQuery.refetch();
    },
  });

  const handleWhoAreYouModalOk = async (username?: string) => {
    if (username) {
      saveUsername(username);
      whoAreYouModalRef.current?.close();
    }
  };

  const deviceList = deviceListQuery.data?.data || [];
  useEffect(() => {
    if (!isDetecting && !isConnected && !forceWaiting && !isPending) {
      window.location.href = `/${school}/auth`;
    }
  }, [isDetecting && isConnected && forceWaiting && isPending]);
  return (
    <div className=" w-full px-8 md:px-0 py-8 md:py-0">
      <Helmet>
        <title>{browserTitle}</title>
      </Helmet>

      <Title>在线会话列表</Title>
      <div className="flex justify-end mt-2">
        <Button
          icon={<ReloadOutlined disabled={deviceListQuery.isFetching} spin={deviceListQuery.isFetching} onClick={() => deviceListQuery.refetch()} />}
          type="link"
        >
          刷新
        </Button>
      </div>
      <List<SessionDevice>
        loading={deviceListQuery.isLoading || deleteMacMutation.isLoading || disconnectMutation.isLoading || isDetecting || forceWaiting}
        locale={{ emptyText: '无在线设备' }}
        style={{ height: 280, overflow: 'auto' }}
        dataSource={deviceList}
        renderItem={(sessionDevice) => {
          const { device } = sessionDevice;
          const lowerDevice = device?.toLocaleLowerCase();
          const deviceNameMap = configrations?.deviceListPage?.showDeviceName;
          const deviceTypeName = deviceNameMap?.[lowerDevice] || device;

          return (
            <List.Item>
              <div className={styles.name}>
                <div className="flex gap-x-1 items-center">
                  <VisibleControl visible={displaySessionInfo.includes('deviceType')}>
                    <DeviceType device={sessionDevice?.device} deviceName={deviceTypeName} />
                  </VisibleControl>
                  <VisibleControl visible={displaySessionInfo.includes('macAddress')}>
                    <span className="flex items-center h-full">{sessionDevice?.callingStationId || sessionDevice?.mac}</span>
                  </VisibleControl>
                </div>
                <div style={{ cursor: 'pointer' }} onClick={() => deleteMacMutation.mutate(sessionDevice)} aria-hidden>
                  <span className="text-base text-orange-500">下线</span>
                </div>
              </div>
            </List.Item>
          );
        }}
      />
      <div className={styles.footer}>
        <Link className={styles.msg} to={`/${school}/limit`}>
          关于在线设备数上限的说明
        </Link>
        <VisibleControl visible={showReconnectButton}>
          <Button className={styles.button} loading={reConnectMutation.isLoading} onClick={() => reConnectMutation.mutate()} type="primary">
            连接网络
          </Button>
        </VisibleControl>
        <div className="h-3"></div>
        <Back />
      </div>
      <VisibleControl visible={!configrations?.deviceListPage?.whoAreYouModal?.disable}>
        <WhoAreYouModal
          ref={whoAreYouModalRef}
          onOk={handleWhoAreYouModalOk}
          title={configrations?.deviceListPage?.whoAreYouModal?.title}
          okText={configrations?.deviceListPage?.whoAreYouModal?.okButtonText}
          cancelText={configrations?.deviceListPage?.whoAreYouModal?.cancelButtonText}
          tipText={configrations?.deviceListPage?.whoAreYouModal?.tip}
          authWays={configrations?.deviceListPage?.whoAreYouModal?.authWays}
        />
      </VisibleControl>
    </div>
  );
}

export default DeviceList;
