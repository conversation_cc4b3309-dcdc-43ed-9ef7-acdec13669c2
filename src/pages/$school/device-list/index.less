.title {
  text-align: center;
  font-size: 16px;
  color: @heading-color;
  margin-bottom: 64px;
  font-weight: bold;
}
.msg {
  font-family: @font-Regular;
  font-size: 14px;
  color: @text-color-secondary;
  text-align: center;
  margin-bottom: 24px;
  text-decoration: underline;
  cursor: pointer;
}
.myItem {
  padding: 12px 0;
  line-height: 1.5;
}
.empty {
  color: @text-color-secondary;
  font-size: 14px;
  padding: 16px;
  text-align: center;
}
.name {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0px 10px;
  p {
    font-family: @font-Medium;
    font-size: 16px;
    color: @text-color;
    letter-spacing: 0;
    margin: 0px;
  }
  .offLine {
    font-size: 16px;
    letter-spacing: 0;
  }
}

.button {
  width: 100%;
  height: 50px;
  border-radius: 4px;
}
.footer {
  margin-bottom: 0px;
  width: 100%;
  .msg {
    font-family: @font-Regular;
    font-size: 14px;
    color: @text-color-secondary;
    margin-bottom: 24px;
    text-align: center;
    text-decoration: underline;
    cursor: pointer;
    display: block;
  }
  .button {
    width: 100%;
    height: 50px;
    border-radius: @border-radius-base;
  }
}
