import PublicAccount from '@/components/PublicAccount';
import useUrlSchool from '@/hooks/useUrlSchool';
import { history } from '@umijs/max';
import { But<PERSON>, Row } from 'antd';
import React from 'react';

const FindSuccess: React.FC = () => {
  const school = useUrlSchool();
  return (
    <div className="w-full px-8 md:px-0 py-8 md:py-0">
      <Row justify="center" align="middle" style={{ marginBottom: 62 }}>
        <p>
          <img src="/findSuccess/check-circle.svg" alt="findSuccess" style={{ marginRight: 10 }} />
          成功设置密码
        </p>
      </Row>
      <PublicAccount />
      <Row justify="center" align="middle">
        <Button onClick={() => history.replace(`/${school}/auth`)} style={{ display: 'block' }}>
          返回
        </Button>
      </Row>
    </div>
  );
};
export default FindSuccess;
