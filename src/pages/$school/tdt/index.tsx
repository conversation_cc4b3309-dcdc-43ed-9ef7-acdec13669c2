import useConfigration from '@/hooks/useConfigration';
import { getDeviceType } from '@/utils';
import React from 'react';
import { UAParser } from 'ua-parser-js';
const parser = new UAParser();

const TestDeviceType: React.FC = () => {
  const configrations = useConfigration();
  const tabletIsPc = configrations?.systemConfig?.tabletIsPc;
  const devicetypeUaFirst = configrations?.systemConfig?.devicetypeUaFirst;
  const dt = getDeviceType({
    tabletIsPc,
    devicetypeUaFirst,
  });
  const res = JSON.stringify(parser.getResult());
  return (
    <div>
      <div>tabletIsPc：{String(tabletIsPc)}</div>
      <div>devicetypeUaFirst：{String(devicetypeUaFirst)}</div>
      <div>{dt}</div>
      <div>{res}</div>
    </div>
  );
};

export default TestDeviceType;
