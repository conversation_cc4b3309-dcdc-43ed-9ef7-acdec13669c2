import { resetPasswordByOldPassword } from '@/api/portal';
import Loading from '@/components/Loading';
import NFrom from '@/components/NForm';
import Title from '@/components/Title';
import useConfigration from '@/hooks/useConfigration';
import useUrlSchool from '@/hooks/useUrlSchool';
import { joinPrefixOrSuffix } from '@/utils';
import { Helmet, history, useMutation } from '@umijs/max';
import { Button, Form, Input, message } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';

type FormValue = {
  webAuthUser: string;
  webAuthPassword: string;
  password: string;
  confirm: string;
};
const FormItem = Form.Item;
const ResetPassword: React.FC = () => {
  const [form] = Form.useForm();
  const [btnDisable, setBtnDisable] = useState(true);
  const school = useUrlSchool();
  const configrations = useConfigration();
  const prependForm = configrations?.resetPasswordPage?.prependForm;

  useEffect(() => {
    const weakPasswordInfoJson = localStorage.getItem('weakPasswordInfo');
    if (weakPasswordInfoJson) {
      const weakPasswordInfo = JSON.parse(weakPasswordInfoJson);
      form.setFieldsValue({
        webAuthUser: weakPasswordInfo.webAuthUser,
        webAuthPassword: weakPasswordInfo.webAuthPassword,
      });
    }
  }, []);
  // 有值表示用户已经通过了账号密码认证，但是密码强度不够，需要用户重新设置密码
  const weakPasswordInfo = useMemo(() => {
    const weakPasswordInfoJson = localStorage.getItem('weakPasswordInfo');
    return weakPasswordInfoJson ? JSON.parse(weakPasswordInfoJson) : null;
  }, []);
  const mutation = useMutation(resetPasswordByOldPassword, {
    onSuccess() {
      message.success('修改密码成功');
      localStorage.removeItem('weakPasswordInfo');
      history.replace(`/${school}/auth`);
    },
  });
  const handleSubmit = (values: FormValue) => {
    if (values.confirm !== values.password) {
      message.warn('两次密码输入不一致');
      return;
    }
    const fullWebAuthUser = joinPrefixOrSuffix(
      values.webAuthUser,
      {
        prefixType: configrations?.resetPasswordPage?.form?.webAuthUser?.prefixType || 'none',
        prefixValue: configrations?.resetPasswordPage?.form?.webAuthUser?.prefixValue || '',
        suffixType: 'none',
        suffixValue: '',
      },
      values
    );
    mutation.mutate({
      webAuthUser: fullWebAuthUser,
      webAuthPassword: values.webAuthPassword,
      webAuthNewPassword: values.password,
    });
  };

  const handleInputChange = () => {
    const { password, confirm } = form.getFieldsValue();
    if (password && confirm) {
      setBtnDisable(false);
    } else {
      setBtnDisable(true);
    }
  };
  if (!configrations) {
    return <Loading />;
  }

  return (
    <div className="w-full px-8 md:px-0 py-8 md:py-0">
      <Helmet>
        <title>修改密码</title>
      </Helmet>
      <Title>修改密码</Title>
      <div className="h-6"></div>
      <div>{!!weakPasswordInfo ? <p>为了您的账号安全，请设置新密码</p> : ''}</div>
      <Form onFinish={handleSubmit} form={form} validateTrigger="onBlur">
        {prependForm?.map((item, idx) => (
          <NFrom authFormItem={item} key={idx} />
        ))}
        <FormItem
          name="webAuthUser"
          help={configrations?.resetPasswordPage?.form?.webAuthUser?.description}
          rules={[{ required: true, message: '请输入用户名' }]}
          hidden={!!weakPasswordInfo}
        >
          <Input size="large" placeholder="上网账号/学号" allowClear />
        </FormItem>
        <FormItem
          name="webAuthPassword"
          hidden={!!weakPasswordInfo}
          help={configrations?.resetPasswordPage?.form?.webAuthPassword?.description}
          rules={[{ required: true, message: configrations?.resetPasswordPage?.form?.webAuthPassword?.errorTip || '请输入密码' }]}
        >
          <Input size="large" type="password" placeholder="旧密码" allowClear />
        </FormItem>
        <FormItem
          name="password"
          help={configrations?.resetPasswordPage?.form?.webAuthNewPassword?.description}
          rules={[
            {
              required: true,
              whitespace: true,
              message: '请设置新密码',
            },
            {
              pattern: new RegExp(configrations?.resetPasswordPage?.passwordReg || ''),
              message: configrations?.resetPasswordPage?.form?.webAuthNewPassword?.errorTip || '密码不符合规则',
            },
          ]}
        >
          <Input onChange={handleInputChange} size="large" type="password" placeholder="设置新密码" allowClear />
        </FormItem>
        <FormItem
          name="confirm"
          rules={[
            {
              required: true,
              whitespace: true,
              message: '确认密码',
            },
          ]}
        >
          <Input onChange={handleInputChange} size="large" type="password" placeholder="确认密码" allowClear />
        </FormItem>
        <FormItem>
          <Button disabled={btnDisable} htmlType="submit" size="large" block type="primary">
            提交
          </Button>
        </FormItem>
      </Form>
    </div>
  );
};

export default ResetPassword;
