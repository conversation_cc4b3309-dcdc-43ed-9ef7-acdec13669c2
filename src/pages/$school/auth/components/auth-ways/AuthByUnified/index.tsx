import useConfigration from '@/hooks/useConfigration';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import { message, notification } from 'antd';
import { get } from 'lodash';
import React, { useMemo } from 'react';
import CsiicThemeButton from '@/components/Buttons/CsiicThemeButton';

const AuthByUnified: React.FC = () => {
  const configrations = useConfigration();
  const messages = get(configrations, 'authPage.messages', {}) as Record<string, string>;
  const [getRedirectUrl] = useRedirectUrl();
  const unifiedAuthConfig = useMemo(() => {
    const authWays = configrations?.authPage?.authWays;
    return authWays?.find((item) => item.type === 'unified') as AuthByUnified;
  }, [configrations]);

  const tip1 = unifiedAuthConfig?.tip1 || '';
  const tipsAtBottom = unifiedAuthConfig?.tipsAtBottom || '';

  const handleClickUnifiedAuth = () => {
    const unifiedUrl = unifiedAuthConfig?.unifiedUrl;
    const redirectUrl = getRedirectUrl();
    if (!redirectUrl) {
      const autoRedirectUrl = get(configrations, 'systemConfig.autoRedirectUrl', '');
      const autoRedirectDelay = get(configrations, 'systemConfig.autoRedirectDelay', 2);
      if (autoRedirectUrl) {
        message.warn(messages.redirectError || '重定向参数错误，请在页面刷新后重试', autoRedirectDelay, () => {
          window.location.href = autoRedirectUrl;
        });
      }
      return;
    }
    if (!unifiedUrl) {
      notification.error({
        message: '统一身份认证地址未配置',
      });
      return;
    }
    window.location.href = unifiedUrl;
  };

  return (
    <div>
      <div className="w-full  mt-4">
        <div className="min-w-[208px] min-h-[208px] flex justify-center items-center">
          <div className="flex flex-col items-center justify-center">
            <img src="/images/login-clock.png" className="w-[180px] h-[154px]" alt="统一身份认证" />
            <div className="text-center text-sm text-gray-500 mt-2 mb-2">
              <span className="inline-block md:block text-sm md:text-base">完成统一授权、统一认证即可上网</span>
              <span className="font-bold inline-block md:block text-sm md:text-base">{tip1}</span>
            </div>
            <CsiicThemeButton onClick={handleClickUnifiedAuth}>立即前往统一身份认证</CsiicThemeButton>
            <div className="text-red-500 text-xs mt-4 z-10 relative">
              <div dangerouslySetInnerHTML={{ __html: tipsAtBottom }} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthByUnified;
