import { getMpLink } from '@/api/api';
import VisibleControl from '@/components/VisibleControl';
import useConfigration from '@/hooks/useConfigration';
import useDetectNetWork from '@/hooks/useDetect';
import useInterval from '@/hooks/useInterval';
import useQRCode from '@/hooks/useQRCode';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useUrlSchool from '@/hooks/useUrlSchool';
import { getDeviceType } from '@/utils';
import { QuestionCircleFilled, ReloadOutlined } from '@ant-design/icons';
import { useNavigate } from '@umijs/max';
import { Button, Popover } from 'antd';
import React, { useEffect } from 'react';

const deviceType = getDeviceType({});

const AuthByWechatMp: React.FC = () => {
  const configrations = useConfigration();
  const autoRedirectUrl = configrations?.systemConfig?.autoRedirectUrl;
  const [getRedirectUrl] = useRedirectUrl();
  const redirectUrl = getRedirectUrl();
  const school = useUrlSchool();
  const navigate = useNavigate();
  const [isConnected, , redetect] = useDetectNetWork();
  const qrCodeValue = JSON.stringify({
    redirectUrl,
    deviceType,
  });
  const [canvasRef] = useQRCode(qrCodeValue, {
    onError(error) {
      console.log(error);
    },
  });
  const handleRetry = () => {
    if (autoRedirectUrl) {
      window.location.href = autoRedirectUrl;
    }
  };

  const handleClickOpenMp = async () => {
    const { data: res } = await getMpLink({
      redirectUrl,
    });
    if (res?.redirectWechatUrl) {
      window.location.href = res.redirectWechatUrl;
    }
  };

  useInterval(() => {
    redetect();
  }, 3 * 1000);

  useEffect(() => {
    if (isConnected && school) {
      navigate(`/${school}/success`);
    }
  }, [isConnected]);
  return (
    <div>
      <div className="text-center">
        <span className="">使用小程序“Wenet来了”，扫描下方二维码进行认证</span>
      </div>
      <div className="w-full  mt-4">
        <div className="min-w-[208px] min-h-[208px] flex justify-center items-center">
          <VisibleControl
            visible={!!redirectUrl}
            fallback={
              <div className="w-full h-full flex justify-center items-center">
                <span className="text-blue-500 cursor-pointer text-sm" onClick={handleRetry}>
                  <span className="mr-1">点击重试</span>
                  <ReloadOutlined />
                </span>
              </div>
            }
          >
            <canvas ref={canvasRef}></canvas>
          </VisibleControl>
        </div>
      </div>

      <div className="flex justify-center w-full">
        <Button type="primary" color="green" onClick={handleClickOpenMp}>
          打开小程序
        </Button>
      </div>

      <div className="flex justify-end">
        <Popover
          content={
            <div className="text-sm">
              <span>微信扫描二维码，进入小程序</span>
              <div className="mt-2 w-52 h-52">
                <img className="w-full h-full object-center object-cover" src="/images/mp-qrcode.jpg" />
              </div>
            </div>
          }
          placement="leftBottom"
        >
          <a>
            <QuestionCircleFilled className="mr-1" />
            如何获取小程序
          </a>
        </Popover>
      </div>
    </div>
  );
};

export default AuthByWechatMp;
