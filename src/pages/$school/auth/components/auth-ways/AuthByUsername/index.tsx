import { checkPassword, connect } from '@/api/portal';
import CsiicThemeButton from '@/components/Buttons/CsiicThemeButton';
import NFrom from '@/components/NForm';
import VisibleControl from '@/components/VisibleControl';
import { useAuthSuccessHandler } from '@/hooks/useAuthSuccessHandler';
import useConfigration from '@/hooks/useConfigration';
import useLocalStorage from '@/hooks/useLocalStorage';
import useMultiDproxy from '@/hooks/useMultiDproxy';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useUrlSchool from '@/hooks/useUrlSchool';
import { getDeviceType, joinPrefixOrSuffix, saveToken, usernameForCheck } from '@/utils';
import CryptoCk from '@/utils/crypto-ck';
import { Link, useMutation, useNavigate } from '@umijs/max';
import { Checkbox, Form, message } from 'antd';
import { AxiosError } from 'axios';
import { get } from 'lodash';
import React, { useEffect, useRef } from 'react';
import errorHandler, { commonErrorHandler } from '../../../_error-handler';
import OverConncurrecyModal, { useOverConncurrecyModal } from '../../OverConncurrecyModal';

interface Any {
  [key: string]: string;
}

const AuthByUsername: React.FC = () => {
  const configrations = useConfigration();
  const authPage = configrations?.authPage;
  const messages = get(configrations, 'authPage.messages', {}) as Record<string, string>;

  const tipsAtBottom = authPage?.authWays?.find((item) => item.type === 'username')?.tipsAtBottom || '';
  console.log(tipsAtBottom);

  const school = useUrlSchool();
  const [agreementChecked, setAgreementChecked] = React.useState(false);
  const [rememberMeChecked, setRememberMeChecked] = React.useState(false);
  const overConncurrecyModalRef = useOverConncurrecyModal();
  const [form] = Form.useForm();
  const submitValuesCacheRef = useRef<any>();
  const navigage = useNavigate();
  const [getRedirectUrl] = useRedirectUrl();
  const { authForm, refetchDproxyAccount, selectedDproxyAccount, isUserSelectedMultiDproxy, enableMultiDproxy } = useMultiDproxy(form);
  const handleSuccess = useAuthSuccessHandler();
  const tabletIsPc = configrations?.systemConfig?.tabletIsPc;
  const devicetypeUaFirst = configrations?.systemConfig?.devicetypeUaFirst;
  const [, setCachedWebAuthUser] = useLocalStorage('webAuthUserForCheckMarkUp', {});

  const rememberInfo = () => {
    const formValues = form.getFieldsValue();
    configrations?.authPage?.authForm.forEach((item) => {
      if (item.type === 'password') {
        if (rememberMeChecked) {
          CryptoCk.set(item.name, formValues[item.name], {
            expires: 180,
          });
          CryptoCk.set('remember', 'true', {
            expires: 180,
          });
        } else {
          CryptoCk.remove(item.name);
          CryptoCk.set('remember', 'false', {
            expires: 180,
          });
        }
      }
      // 记住密码之外的表单
      if (item.type !== 'password' && item.rememberable) {
        CryptoCk.set(item.name, formValues[item.name], {
          expires: 180,
        });
      } else if (item.type !== 'password' && !item.rememberable) {
        CryptoCk.remove(item.name);
      }
    });
  };

  const handlInputBlur = (formItemName: string) => {
    if (formItemName === 'webAuthUser') {
      refetchDproxyAccount();
    }
  };

  // 处理超并发
  const handleOverConcurrency = () => {
    overConncurrecyModalRef.current?.show();
  };

  const checkPasswordMutation = useMutation(checkPassword, {
    onSuccess() {
      const { webAuthUser, webAuthPassword } = submitValuesCacheRef.current;
      localStorage.setItem('weakPasswordInfo', JSON.stringify({ webAuthUser, webAuthPassword }));
      navigage(`/${school}/reset-password`, { replace: true });
    },
    onError(err: AxiosError<any>) {
      if (err.response?.data?.code === 1023) {
        message.error(configrations?.successPage?.disconnectWhoAreYouModal?.messages?.invalidUsernameOrPassword || '用户名或密码错误');
      }
    },
  });
  const authMutation = useMutation(connect, {
    onSuccess({ data }) {
      if (data && data.token) {
        saveToken(data.token);
      }
      if (data && data.statusCode === 200) {
        rememberInfo();
        const cachedFormValues = submitValuesCacheRef.current;
        usernameForCheck.set(cachedFormValues.webAuthUser);
        handleSuccess();
      } else if (data) {
        const { statusCode, error } = data;
        const cachedFormValues = submitValuesCacheRef.current;
        if (errorHandler[statusCode] && errorHandler[statusCode][error]) {
          errorHandler[statusCode][error].call(null, data, rememberInfo, {
            onOverConcurrency: handleOverConcurrency,
            configrations: configrations,
            connectParams: cachedFormValues,
          });
        } else {
          commonErrorHandler(data);
        }
      }
    },
    onError(error: AxiosError<any>) {
      const err = error.response?.data;
      const errorMsg = err.error;
      if (errorMsg === 'READ_USER_IP_ERROR') {
        const autoRedirectUrl = get(configrations, 'systemConfig.autoRedirectUrl', '');
        const autoRedirectDelay = get(configrations, 'systemConfig.autoRedirectDelay', 2);
        if (autoRedirectUrl) {
          message.warn(messages.redirectError || '重定向参数错误，请在页面刷新后重试', autoRedirectDelay, () => {
            window.location.href = autoRedirectUrl;
          });
        }
      }
    },
  });
  // 去认证
  const finish = async (values: Any) => {
    if (configrations) {
      const { authPage } = configrations;
      const checkPasswordSafety = authPage.checkPasswordSafety;
      const passwordReg = new RegExp(authPage?.passwordReg || '');
      const formValue: Any = {};
      if (authPage?.agreeLimit && !agreementChecked) {
        message.error(messages.agreeLimit || '请同意用户协议和隐私政策');
        return;
      }
      authPage?.authForm?.forEach((item) => {
        if (item.type === 'input' && item.isCarry) {
          formValue[item.name] = joinPrefixOrSuffix(values[item.name], item, values);
          // 当启用多账期并且用户选择了账期时，将代拨用户名拼接到用户名后面
          if (item.name === 'webAuthUser' && enableMultiDproxy && isUserSelectedMultiDproxy && selectedDproxyAccount) {
            formValue[item.name] = formValue[item.name] + `#${selectedDproxyAccount}`;
          }
        }
        if (item.type === 'select' && item.isCarry) {
          formValue[item.name] = values[item.name];
        }
        if (item.type === 'password' && item.isCarry) {
          formValue[item.name] = values[item.name];
        }
      });
      const p = {
        deviceType: getDeviceType({
          tabletIsPc,
          devicetypeUaFirst,
        }),
        redirectUrl: encodeURI(getRedirectUrl()),
        ...formValue,
      };

      submitValuesCacheRef.current = p;
      const { webAuthUser, webAuthPassword } = formValue;
      setCachedWebAuthUser({ webAuthUser });
      // 检查密码强度的正则，8-16位，包含数字、字母
      if (checkPasswordSafety && !passwordReg.test(webAuthPassword)) {
        await checkPasswordMutation.mutateAsync({ webAuthUser, webAuthPassword });
      } else {
        await authMutation.mutateAsync(p);
      }
    }
  };
  useEffect(() => {
    const initialFormValue: Any = {};
    configrations?.authPage?.authForm.forEach((item) => {
      const formKey = item.name;
      if (CryptoCk.get('remember') === 'true') {
        setRememberMeChecked(true);
      }
      const formValue = CryptoCk.get(formKey);
      if (formValue) {
        initialFormValue[formKey] = formValue;
      }
    });
    if (Object.keys.length) {
      form.setFieldsValue(initialFormValue);
    }
  }, []);
  if (!configrations) {
    return <></>;
  }
  return (
    <div>
      <Form form={form} onFinish={finish} layout="horizontal" className="w-full box-border" validateTrigger="onBlur">
        {authForm?.map((item) => (
          <NFrom onInputBlur={handlInputBlur} key={item.name} authFormItem={item} />
        ))}
        <Checkbox style={{ marginBottom: 16 }} checked={rememberMeChecked} onChange={() => setRememberMeChecked(!rememberMeChecked)}>
          {authPage?.rememberMeText}
        </Checkbox>
        <VisibleControl visible={!!authPage?.agreeLimit}>
          <div>
            <Checkbox style={{ marginBottom: 16 }} checked={agreementChecked} onChange={() => setAgreementChecked(!agreementChecked)}>
              <span>我同意</span>
              <Link to={`/${school}/user-agreement`}>用户协议</Link>
              <span>和</span>
              <Link to={`/${school}/privacy-policy`}>隐私政策</Link>
            </Checkbox>
          </div>
        </VisibleControl>
        <Form.Item>
          <CsiicThemeButton loading={authMutation.isLoading} htmlType="submit">
            {authPage?.authButtonText}
          </CsiicThemeButton>
        </Form.Item>
      </Form>
      <VisibleControl visible={!!tipsAtBottom}>
        <div className="text-red-400 font-bold text-xs pb-2" dangerouslySetInnerHTML={{ __html: tipsAtBottom }} />
      </VisibleControl>
      <OverConncurrecyModal modalTitle={authPage?.concurrentModal?.title} ref={overConncurrecyModalRef} />
    </div>
  );
};

export default AuthByUsername;
