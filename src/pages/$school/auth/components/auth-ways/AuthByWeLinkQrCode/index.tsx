import { connectByWelinkCode } from '@/api/portal';
import VisibleControl from '@/components/VisibleControl';
import { useAuthSuccessHandler } from '@/hooks/useAuthSuccessHandler';
import useConfigration from '@/hooks/useConfigration';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useWeLinkQrcode from '@/hooks/useWeLinkQrcode';
import { getDeviceType, saveToken } from '@/utils';
import { ReloadOutlined } from '@ant-design/icons';
import React from 'react';
import errorHandler, { commonErrorHandler } from '../../../_error-handler';
import OverConncurrecyModal, { useOverConncurrecyModal } from '../../OverConncurrecyModal';

const deviceType = getDeviceType({});

const AuthByWeLinkQrCode: React.FC = () => {
  const configrations = useConfigration();
  const autoRedirectUrl = configrations?.systemConfig?.autoRedirectUrl;
  const [getRedirectUrl] = useRedirectUrl();
  const redirectUrl = getRedirectUrl();
  const overConncurrecyModalRef = useOverConncurrecyModal();
  const handleSuccess = useAuthSuccessHandler();
  // 处理超并发
  const handleOverConcurrency = () => {
    overConncurrecyModalRef.current?.show();
  };
  const client_id = configrations?.authPage?.authByWeLinkQrCode?.client_id || '';
  const redirect_uri = configrations?.authPage?.authByWeLinkQrCode?.redirect_uri || '';
  const [qrcodeRef] = useWeLinkQrcode({
    client_id,
    redirect_uri,
    async onGetCode(code) {
      try {
        const { data } = await connectByWelinkCode({
          code,
          deviceType,
          redirectUrl,
        });
        if (data && data.token) {
          saveToken(data.token);
        }
        if (data && data.statusCode === 200) {
          handleSuccess();
        } else if (data) {
          const { statusCode, error } = data;
          if (errorHandler[statusCode] && errorHandler[statusCode][error]) {
            errorHandler[statusCode][error].call(null, data, () => {}, {
              onOverConcurrency: handleOverConcurrency,
              configrations: configrations,
            });
          } else {
            commonErrorHandler(data);
          }
        }
      } catch (error) {}
    },
  });

  const handleRetry = () => {
    if (autoRedirectUrl) {
      window.location.href = autoRedirectUrl;
    }
  };

  return (
    <div>
      <div className="w-full  mt-4">
        <div className="min-w-[208px] min-h-[208px] flex justify-center items-center">
          <VisibleControl
            visible={!!redirectUrl}
            fallback={
              <div className="w-full h-full flex justify-center items-center">
                <span className="text-blue-500 cursor-pointer text-sm" onClick={handleRetry}>
                  <span className="mr-1">点击重试</span>
                  <ReloadOutlined />
                </span>
              </div>
            }
          >
            <div className="" ref={qrcodeRef} id="qrcode_container"></div>
          </VisibleControl>
        </div>
      </div>
      <OverConncurrecyModal modalTitle={configrations?.authPage?.concurrentModal?.title} ref={overConncurrecyModalRef} />
    </div>
  );
};

export default AuthByWeLinkQrCode;
