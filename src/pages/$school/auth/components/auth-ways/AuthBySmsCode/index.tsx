import { connectBySmsCode, getMultiDproxyAccounts, sendVerifyCode } from '@/api/portal';
import { DproxyAccount } from '@/api/portalData';
import CsiicThemeButton from '@/components/Buttons/CsiicThemeButton';
import NFrom from '@/components/NForm';
import TimingButton from '@/components/TimingButton';
import { useAuthSuccessHandler } from '@/hooks/useAuthSuccessHandler';
import useConfigration from '@/hooks/useConfigration';
import useDetectNetWork from '@/hooks/useDetect';
import useLocalStorage from '@/hooks/useLocalStorage';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useUrlSchool from '@/hooks/useUrlSchool';
import { getDeviceType, getIspName, joinPrefixOrSuffix, saveToken, usernameForCheck } from '@/utils';
import regs from '@/utils/regs';
import { useMutation, useNavigate } from '@umijs/max';
import { Form, Input, message, Radio, Select } from 'antd';
import { AxiosError } from 'axios';
import { get } from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import errorHandler, { commonErrorHandler } from '../../../_error-handler';
import OverConncurrecyModal, { useOverConncurrecyModal } from '../../OverConncurrecyModal';

type FormValue = {
  webAuthUser: string;
  verifyCode: string;
  'multiDproxy-dproxyAccount'?: string;
};

const AuthBySmsCode: React.FC = () => {
  const configrations = useConfigration();
  const messages = get(configrations, 'authPage.messages', {}) as Record<string, string>;
  const [accountList, setAccountList] = useState<DproxyAccount[]>([]);
  const [form] = Form.useForm<FormValue>();
  const smsCodeAuthConfig = useMemo(() => {
    const authWays = configrations?.authPage?.authWays;
    return authWays?.find((item) => item.type === 'sms-code') as AuthBySmsCode;
  }, [configrations]);
  const [cachedParams, setCachedParams] = useLocalStorage<{ webAuthUser: string }>('smsCodeAuth', { webAuthUser: '' });
  const [, setCachedWebAuthUser] = useLocalStorage('webAuthUserForCheckMarkUp', {});

  const webAuthUserReg = smsCodeAuthConfig?.mobileInputRegex ? new RegExp(smsCodeAuthConfig?.mobileInputRegex) : regs.phone;
  const submitValuesCacheRef = useRef<any>();
  const [getRedirectUrl] = useRedirectUrl();
  const school = useUrlSchool();
  const navigate = useNavigate();
  const handleSuccess = useAuthSuccessHandler();
  const overConncurrecyModalRef = useOverConncurrecyModal();
  const [isConnected] = useDetectNetWork();
  const useEnableMultiDproxy = Form.useWatch('multiDproxy-authMode', form);
  // const isUserSelectedMultiDproxy = useEnableMultiDproxy === 'isp'
  const isUserSelectedMultiDproxy = false; // 暂时关闭
  // const enableMultiDproxy = configrations?.authPage?.enableMultiDproxy;
  const enableMultiDproxy = false;
  const multiDproxyConfig = configrations?.authPage?.multiDproxyConfig;
  const tabletIsPc = configrations?.systemConfig?.tabletIsPc;
  const devicetypeUaFirst = configrations?.systemConfig?.devicetypeUaFirst;

  const multiDproxyAccountOptions = useMemo(() => {
    return accountList.map((item) => ({
      label: `${getIspName(item.dproxyISP)} ${item.dproxyUsername || multiDproxyConfig?.multiDproxyAccount?.unbindTip || '未绑定运营商账号'}`,
      value: item.dproxyUsername,
      disabled: !item.dproxyUsername,
    }));
  }, [accountList, multiDproxyConfig]);

  useEffect(() => {
    if (cachedParams?.webAuthUser) {
      form.setFieldValue('webAuthUser', cachedParams.webAuthUser);
    }
  }, []);

  // 处理超并发
  const handleOverConcurrency = () => {
    overConncurrecyModalRef.current?.show();
  };

  const connectMutation = useMutation(connectBySmsCode, {
    onSuccess({ data }) {
      if (data && data.token) {
        saveToken(data.token);
      }
      if (data && data.statusCode === 200) {
        const cachedFormValues = submitValuesCacheRef.current;
        usernameForCheck.set(cachedFormValues.webAuthUser);
        handleSuccess();
      } else if (data) {
        const { statusCode, error } = data;
        if (errorHandler[statusCode] && errorHandler[statusCode][error]) {
          errorHandler[statusCode][error].call(null, data, () => {}, {
            onOverConcurrency: handleOverConcurrency,
            configrations: configrations,
          });
        } else {
          commonErrorHandler(data);
        }
      }
    },
    onError(error: AxiosError) {
      const err = error.response?.data;
      const errorMsg = err.error;
      if (errorMsg === 'READ_USER_IP_ERROR') {
        const autoRedirectUrl = get(configrations, 'systemConfig.autoRedirectUrl', '');
        const autoRedirectDelay = get(configrations, 'systemConfig.autoRedirectDelay', 2);
        if (autoRedirectUrl) {
          message.warn(messages.redirectError || '重定向参数错误，请在页面刷新后重试', autoRedirectDelay, () => {
            window.location.href = autoRedirectUrl;
          });
        }
      }
    },
  });

  const handleFinish = async (value: FormValue) => {
    const { verifyCode, webAuthUser } = value;
    const redirectUrl = getRedirectUrl();
    const params = {
      webAuthUser,
      verifyCode,
      redirectUrl,
      deviceType: getDeviceType({
        devicetypeUaFirst,
        tabletIsPc,
      }),
    };
    const { mobilePrefixType, mobilePrefixValue, mobileSuffixType, mobileSuffixValue } = smsCodeAuthConfig;
    params.webAuthUser = joinPrefixOrSuffix(
      webAuthUser,
      {
        prefixType: mobilePrefixType,
        prefixValue: mobilePrefixValue,
        suffixType: mobileSuffixType,
        suffixValue: mobileSuffixValue,
      },
      value
    );
    if (isUserSelectedMultiDproxy && enableMultiDproxy) {
      params.webAuthUser = `${value.webAuthUser}#${value['multiDproxy-dproxyAccount']}`;
    }
    submitValuesCacheRef.current = params;
    setCachedParams({ webAuthUser: webAuthUser });
    setCachedWebAuthUser({ webAuthUser });
    return connectMutation.mutateAsync(params);
  };

  const handleClickSendSmsCode = async () => {
    const { webAuthUser } = await form.validateFields(['webAuthUser']);
    const formValue = form.getFieldsValue();
    const { mobilePrefixType, mobilePrefixValue, mobileSuffixType, mobileSuffixValue } = smsCodeAuthConfig;
    const sendTo = joinPrefixOrSuffix(
      webAuthUser,
      {
        prefixType: mobilePrefixType,
        prefixValue: mobilePrefixValue,
        suffixType: mobileSuffixType,
        suffixValue: mobileSuffixValue,
      },
      formValue
    );
    const res = await sendVerifyCode(sendTo);
    if (res) {
      message.success(smsCodeAuthConfig?.smsCodeSendSuccessTip || '验证码发送成功');
      return true;
    } else {
      message.error(smsCodeAuthConfig?.smsCodeSendErrorTip || '验证码发送失败');
      return Promise.reject(new Error(''));
    }
  };

  const getMultiDproxyAccount = () => {
    if (enableMultiDproxy && isUserSelectedMultiDproxy) {
      const webAuthUser = form.getFieldValue('webAuthUser');
      if (!webAuthUser) {
        setAccountList([]);
        form.setFieldsValue({
          'multiDproxy-dproxyAccount': undefined,
        });
        return;
      }
      getMultiDproxyAccounts({ webAuthUser })
        .then(({ data: res }) => {
          if (res) {
            setAccountList(res);
            if (res.length > 0) {
              // 重新获取列表后，如果之前选择的账期还在列表里面，就不清空，否则清空
              if (!res.find((item) => item.dproxyUsername === form.getFieldValue('multiDproxy-dproxyAccount'))) {
                form.setFieldsValue({
                  'multiDproxy-dproxyAccount': res[0].dproxyUsername,
                });
              }
            } else {
              form.setFieldsValue({
                'multiDproxy-dproxyAccount': undefined,
              });
            }
          }
        })
        .catch(() => {
          setAccountList([]);
          form.setFieldsValue({
            'multiDproxy-dproxyAccount': undefined,
          });
        });
    }
  };

  useEffect(() => {
    if (isUserSelectedMultiDproxy) {
      getMultiDproxyAccount();
    }
  }, [isUserSelectedMultiDproxy]);

  useEffect(() => {
    if (isConnected && school) {
      navigate(`/${school}/success`);
    }
  }, [isConnected]);

  if (!configrations) {
    return <></>;
  }
  const { authPage } = configrations;
  const { prependForm = [] } = smsCodeAuthConfig;
  return (
    <div>
      <Form<FormValue> form={form} layout="horizontal" onFinish={handleFinish} size="large" validateTrigger="onBlur">
        {prependForm.map((item) => (
          <NFrom key={item.name} authFormItem={item} />
        ))}
        <Form.Item
          name="webAuthUser"
          validateFirst
          rules={[
            { required: true, message: smsCodeAuthConfig?.mobileInputEmptyTip },
            { pattern: webAuthUserReg, message: smsCodeAuthConfig?.mobileInputErrorTip },
          ]}
        >
          <Input placeholder={smsCodeAuthConfig?.mobileInputPlaceholder} onBlur={getMultiDproxyAccount} />
        </Form.Item>
        <div className="w-full flex">
          <Form.Item className="flex-1 w-12" name="verifyCode" rules={[{ required: true, message: '请输入验证码' }]}>
            <Input placeholder={smsCodeAuthConfig?.smsCodeInputPlaceholder} />
          </Form.Item>
          <TimingButton textAfterNumber="秒后可重发" min={0} max={60} onClick={handleClickSendSmsCode}>
            {smsCodeAuthConfig?.sendSmsCodeButtonText}
          </TimingButton>
        </div>
        {enableMultiDproxy && (
          <Form.Item name="multiDproxy-authMode" initialValue="campus" rules={[{ required: true }]}>
            <Radio.Group
              options={[
                { label: multiDproxyConfig?.modeRadio?.doNotUseMultiDproxyLabel || '校园网', value: 'campus' },
                { label: multiDproxyConfig?.modeRadio?.useMultiDproxyLabel || '运营商', value: 'isp' },
              ]}
            />
          </Form.Item>
        )}
        {useEnableMultiDproxy === 'isp' && (
          <Form.Item name="multiDproxy-dproxyAccount" rules={[{ required: true, message: '请选择账期' }]}>
            <Select placeholder="请选择账期" options={multiDproxyAccountOptions} />
          </Form.Item>
        )}
        <Form.Item>
          <CsiicThemeButton htmlType="submit">{smsCodeAuthConfig?.loginButtonText}</CsiicThemeButton>
        </Form.Item>
      </Form>
      <OverConncurrecyModal modalTitle={authPage?.concurrentModal?.title} ref={overConncurrecyModalRef} />
    </div>
  );
};

export default AuthBySmsCode;
