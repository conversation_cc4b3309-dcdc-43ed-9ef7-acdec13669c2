import { getSessionList, offline } from '@/api/portal';
import { ConcurrentInfo } from '@/api/portalData';
import DeviceType from '@/components/DeviceType';
import TipWithIcon from '@/components/TipWithIcon';
import VisibleControl from '@/components/VisibleControl';
import useConfigration from '@/hooks/useConfigration';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import { WarningFilled } from '@ant-design/icons';
import { useMutation, useQuery } from '@umijs/max';
import { Button, List, message, Modal } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';

interface OverConncurrecyModalProps {
  modalTitle?: string;
  onShow?: () => void;
  onClose?: () => void;
  isUpdate?: boolean;
  footer?: React.ReactNode;
  onOfflineSuccess?: () => void;
}

type RefType = {
  show: () => void;
};

export function useOverConncurrecyModal() {
  const ref = useRef<RefType>({
    show: () => {},
  });
  return ref;
}

const OverConncurrecyModal = React.forwardRef<RefType, OverConncurrecyModalProps>((props, ref) => {
  const { onShow, onClose, modalTitle = '', footer, onOfflineSuccess } = props;
  const [visible, toggleVisible] = useState<boolean>(false);

  const configrations = useConfigration();
  const displaySessionInfo = configrations?.authPage?.concurrentModal?.displaySessionInfo;
  const [, , { getIp }] = useRedirectUrl();
  const userIp = getIp();

  /** 获取所有在线会话列表 */
  const deviceListQuery = useQuery(['getSessionList', userIp], async () => getSessionList(), {
    enabled: visible,
  });

  // 下线
  const offlineMutation = useMutation(offline, {
    onSuccess() {
      message.success('下线成功');
      onOfflineSuccess?.();
      deviceListQuery.refetch();
    },
  });

  const deviceListInfo: ConcurrentInfo | undefined | null = deviceListQuery.data?.data;

  const handleClick = () => {
    onShow?.();
    toggleVisible(true);
  };

  const handleCancel = () => {
    onClose?.();
    toggleVisible(false);
  };

  useImperativeHandle(ref, () => ({
    show: () => {
      handleClick();
    },
  }));
  return (
    <Modal open={visible} onCancel={handleCancel} title={modalTitle || 'modalTitle'} footer={footer || <Button onClick={handleCancel}>关闭</Button>}>
      <TipWithIcon icon={<WarningFilled />} text="当前超过并发限制，请选择其他设备下线后登录" />
      <List
        loading={deviceListQuery.isLoading || offlineMutation.isLoading}
        style={{ width: '100%' }}
        className="flex-1 overflow-y-auto w-full"
        locale={{ emptyText: '无在线设备' }}
        dataSource={deviceListInfo?.sessions ? deviceListInfo?.sessions : []}
        renderItem={({ acct_unique_id: acctUniqueId, deviceType, calling_station_id }) => {
          const lowerDevice = deviceType?.toLocaleLowerCase();
          const deviceNameMap = configrations?.authPage?.concurrentModal?.showDeviceName;
          const deviceTypeName = deviceNameMap?.[lowerDevice] || deviceType;

          return (
            <List.Item>
              <div className="w-full flex justify-between">
                <div className="flex gap-x-1 items-center">
                  <VisibleControl visible={displaySessionInfo?.includes('deviceType')}>
                    <DeviceType device={deviceType} deviceName={deviceTypeName} />
                  </VisibleControl>
                  <VisibleControl visible={displaySessionInfo?.includes('macAddress')}>
                    <span className="text-secondary-color flex items-center h-full">{calling_station_id}</span>
                  </VisibleControl>
                </div>
                <div
                  style={{ cursor: 'pointer' }}
                  onClick={() =>
                    offlineMutation.mutate({
                      acctUniqueId,
                      mac: calling_station_id,
                    })
                  }
                  aria-hidden
                >
                  <span className="text-base text-orange-500">下线</span>
                </div>
              </div>
            </List.Item>
          );
        }}
      />
    </Modal>
  );
});

export default OverConncurrecyModal;
