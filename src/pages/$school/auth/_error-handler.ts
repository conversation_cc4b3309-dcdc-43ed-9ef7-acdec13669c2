import { ConnectErrorResponse, ConnectParams } from '@/api/portalData';
import { getErrorCode } from '@/utils/error';
import { encrypt } from '@/utils/n-crypto';
import { history } from '@umijs/max';
import { notification } from 'antd';

interface CommonErrorHandler {
  (errorResponse: ConnectErrorResponse, callback?: () => void): void;
}

interface ErrorHandlerOptions {
  onOverConcurrency: () => void;
  configrations?: PortalWebConfigration;
  connectParams?: ConnectParams;
}
interface ErrorHandler {
  [key: number]: {
    [key: number]: (errorResponse: ConnectErrorResponse, callback?: () => void, options?: ErrorHandlerOptions) => void;
  };
}

function errorTip(tip: string) {
  notification.open({
    message: '错误',
    duration: 8,
    description: tip,
    type: 'error',
    placement: 'top',
  });
}

function toArrearagePage(school: string, connectParams?: ConnectParams) {
  const webAuthPassword = connectParams?.webAuthPassword;
  const webAuthUser = connectParams?.webAuthUser;
  if (webAuthUser && webAuthPassword) {
    // 把用户名密码加密带到 arrearage page
    const encryptedUser = encrypt(webAuthUser);
    const encryptedPassword = encrypt(webAuthPassword);
    history.push(`/${school}/arrearage?eu=${encryptedUser}&ep=${encryptedPassword}`);
    return;
  }
  history.push(`/${school}/arrearage`);
}

export const commonErrorHandler: CommonErrorHandler = (errorResponse: ConnectErrorResponse) => {
  const { error, errorDescription } = errorResponse;
  errorTip(errorDescription || getErrorCode(error));
};
const errorHandler: ErrorHandler = {
  400: {
    81: (errorResponse: ConnectErrorResponse, rememberInfo, options) => {
      const messages = options?.configrations?.authPage?.messages;
      const actions = options?.configrations?.authPage?.actions;
      const { errorDescription, error } = errorResponse;
      const school = location.pathname.split('/')[1];
      const connectParams = options?.connectParams;
      if (errorDescription.indexOf('challenge already online') > -1) {
        rememberInfo?.();
        history.replace(`/${school}/success`);
      } else if (errorDescription.indexOf('Your plan not bind, please bind you phone') > -1) {
        errorTip(messages?.noPlanBind || '当前套餐未绑定手机号，前往【wenet服务中心-我的网络-我的套餐-绑定运营商账号】绑定手机号');
        rememberInfo?.();
      } else if (errorDescription.indexOf('You are dialed up outside your allowed timespan') > -1) {
        errorTip(messages?.beLimited || '当前时间，网络访问被限制');
        rememberInfo?.();
      } else if (errorDescription.indexOf('Expired account') > -1) {
        errorTip(messages?.expired || '当前套餐已过期');
        rememberInfo?.();
      } else if (errorDescription.indexOf('You account has been froze, please contact service support') > -1) {
        errorTip(messages?.frozen || '当前账号被冻结，请联系客服');
        rememberInfo?.();
      } else if (errorDescription.indexOf('No billing plan subscription, please subscribe') > -1) {
        rememberInfo?.();
        if (actions?.noBillingPlan === 'topError') {
          errorTip(messages?.noBillingPlan || '当前账号未订购套餐，请订购套餐');
        } else {
          toArrearagePage(school, connectParams);
        }
      } else if (errorDescription.indexOf('详细错误信息(81001-错误的用户名或密码)') > -1 || errorDescription.indexOf('invalid username or') > -1) {
        errorTip(messages?.invalidUsernameOrPassword || '你输入的账号或密码不正确');
      } else if (errorDescription.indexOf('already have') > -1) {
        options?.onOverConcurrency?.();
      } else {
        errorTip(getErrorCode(error) || errorDescription);
      }
    },
    84: (errorResponse: ConnectErrorResponse, rememberInfo, options) => {
      const messages = options?.configrations?.authPage?.messages;
      const actions = options?.configrations?.authPage?.actions;
      const { errorDescription, error } = errorResponse;
      const connectParams = options?.connectParams;
      const school = location.pathname.split('/')[1];
      if (errorDescription.indexOf('invalid username or password') > -1) {
        errorTip(messages?.invalidUsernameOrPassword || '你输入的账号或密码不正确');
      } else if (errorDescription.indexOf('the account can only be used in office zone') > -1) {
        errorTip(messages?.onlyOfficeZone || '该账号仅允许在办公区域使用');
        rememberInfo?.();
      } else if (errorDescription.indexOf('already have') > -1) {
        rememberInfo?.();
        options?.onOverConcurrency?.();
      } else if (errorDescription.indexOf('No billing plan subscription, please subscribe') > -1) {
        rememberInfo?.();
        if (actions?.noBillingPlan === 'topError') {
          errorTip(messages?.noBillingPlan || '当前账号未订购套餐，请订购套餐');
        } else {
          toArrearagePage(school, connectParams);
        }
      } else if (errorDescription.indexOf('您的套餐仅允许周一至周五') > -1) {
        rememberInfo?.();
        errorTip('您的套餐仅允许周一至周五7:50-18:00登录！');
      } else {
        errorTip(getErrorCode(error) || errorDescription);
      }
    },
    86: () => {
      const school = location.pathname.split('/')[1];
      history.replace(`/${school}/success`);
    },
    241: (errorResponse: ConnectErrorResponse) => {
      errorTip(errorResponse.errorDescription);
    },
  },
  500: {
    13: (errorResponse: ConnectErrorResponse, rememberInfo, options) => {
      const messages = options?.configrations?.authPage?.messages;
      errorTip(messages?.invalidUsernameOrPassword || '你输入的账号或密码不正确');
    },
  },
};

export default errorHandler;
