import { getNotification } from '@/api/notification';
import AuthModeSwitch from '@/components/AuthModeSwitch';
import CsButton from '@/components/CsButton';
import NModal from '@/components/NModal';
import SuspensionDriveBox from '@/components/SuspensionDriveBox';
import VisibleControl from '@/components/VisibleControl';
import useConfigration from '@/hooks/useConfigration';
import useDetect from '@/hooks/useDetect';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useUrlSchool from '@/hooks/useUrlSchool';
import { isMobileDevice, parseUrlToObj } from '@/utils';
import { Helmet, useLocation, useQuery } from '@umijs/max';
import { Spin, Tabs, TabsProps } from 'antd';
import { get } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import AuthBySmsCode from './components/auth-ways/AuthBySmsCode';
import AuthByUnified from './components/auth-ways/AuthByUnified';
import AuthByUsername from './components/auth-ways/AuthByUsername';
import AuthByWechatMp from './components/auth-ways/AuthByWechatMp';
import AuthByWeLinkQrCode from './components/auth-ways/AuthByWeLinkQrCode';
import styles from './index.less';

const authWayComponentMap: Record<AuthWayType, React.ReactNode> = {
  username: <AuthByUsername />,
  'wechat-mp': <AuthByWechatMp />,
  'sms-code': <AuthBySmsCode />,
  unified: <AuthByUnified />,
};

const isMobile = isMobileDevice();

const Auth: React.FC = () => {
  const [isScanCodeMode, setIsScanCodeMode] = useState(false);
  const configrations = useConfigration();
  const location = useLocation();
  const school = useUrlSchool();
  // 获取redirectUrl
  const [getRedirectUrl, saveRedirectUrl] = useRedirectUrl();
  const [csPostion, setCsPostion] = useState<{ top: number; left: number }>();
  const { isLoading, data: notificationData } = useQuery(['getNotification-tongzhi'], () => {
    if (configrations) {
      const { schoolNameEn } = configrations;
      return getNotification(`${schoolNameEn}-tongzhi`);
    }
  });

  // 探测网络
  const [isConnected, isDetecting] = useDetect({
    onCompleted(result) {
      if (result) {
        window.location.href = `/${school}/success`;
      }
    },
  });

  // 自动重定向
  const autoRedirect = () => {
    // 配置文件是否允许自动获取重定向地址
    const autoRedirecWhenNoRedirectInfo = get(configrations, 'systemConfig.autoRedirecWhenNoRedirectInfo', false);

    // 当cookie或localStorege中都没有重定向地址且无网络连接的时候，需要创建iframe来获取
    if (autoRedirecWhenNoRedirectInfo && !isConnected && !getRedirectUrl()) {
      /* 获取url上的重定向参数 begin */
      const urlParams = parseUrlToObj(window.location.search);
      let redirectUrlFromUrl;
      if (urlParams?.redirectUrl) {
        redirectUrlFromUrl = decodeURIComponent(urlParams.redirectUrl);
      }
      /* 获取url上的重定向参数 end */

      /**
       * url上没有重定向地址，可能1：用户直接访问了portalweb 可能2：清空了浏览器缓存，重新返回了登录页
       * 创建iframe 加载会被重定向的地址
       */
      if (!redirectUrlFromUrl) {
        const autoRedirectUrl = get(configrations, 'systemConfig.autoRedirectUrl', '');
        if (autoRedirectUrl) {
          const targetUrl = configrations?.systemConfig?.autoRedirectUrl || 'http://neverssl.com';
          const iframe = document.createElement('iframe');
          iframe.src = targetUrl;
          iframe.id = 'redirectUrlIframe';
          iframe.style.display = 'none';
          document.body.appendChild(iframe);
        }
      } else {
        /**
         * url上有重定向地址，发送消息。
         * 这个逻辑会被子页面执行
         */
        const postUrl = configrations?.systemConfig?.iframeMessagePostUrl || window.location.origin;
        console.log(postUrl);
        window.parent.postMessage(
          {
            tag: 'wenet',
            redirectUrl: redirectUrlFromUrl,
          },
          postUrl
        );
      }
    }
  };

  useEffect(() => {
    autoRedirect();
  }, [location.search]);

  const redirectUrlListener = useCallback((e: MessageEvent<any>) => {
    if (e.data?.tag === 'wenet') {
      console.log('****', e.data);
      if (e.data.redirectUrl) {
        const redirectUrlFromIframe = decodeURIComponent(e.data.redirectUrl);
        saveRedirectUrl(redirectUrlFromIframe);
      }
    }
  }, []);

  const getBottomRightPosition = () => {
    const authForm = document.getElementById('authForm');

    if (authForm) {
      const rect = authForm.getBoundingClientRect();
      // 计算右下角的坐标
      const bottomRightX = rect.left + rect.width;
      const bottomRightY = rect.top + rect.height;
      const browserWidth = window.innerWidth;

      // top、left 减去客服图标长宽的一半；当为电脑屏幕时，客服图标再往上移60px
      setCsPostion({
        top: browserWidth >= 980 ? bottomRightY - 90 : bottomRightY - 30,
        left: bottomRightX - 30,
      });
    }
  };

  useEffect(() => {
    getBottomRightPosition();
    // 监听监子iframe窗口的消息
    window.addEventListener('message', redirectUrlListener);
    window.addEventListener('resize', getBottomRightPosition);

    return () => {
      window.removeEventListener('message', redirectUrlListener);
      window.removeEventListener('resize', getBottomRightPosition);
    };
  }, []);

  const authWayItems = useMemo(() => {
    const authWays = configrations?.authPage?.authWays;
    const res: TabsProps['items'] = [];
    authWays?.forEach((item) => {
      if (item.type === 'wechat-mp' && isMobile && item.disableAtMobile) {
        return;
      }
      const target = authWayComponentMap[item.type];
      if (target) {
        res.push({
          label: item.label,
          key: item.type,
          children: target,
        });
      }
    });
    return res;
  }, [configrations]);

  const hasWeLinkQrCode = !!configrations?.authPage?.authByWeLinkQrCode?.enable;

  const isShowAuthByWeLinkQrCode = !isMobile && isScanCodeMode && hasWeLinkQrCode;
  if (configrations) {
    const { authPage } = configrations;

    return (
      <Spin spinning={isDetecting}>
        <Helmet>
          <title>{authPage?.browserTitle}</title>
        </Helmet>
        <div className="rounded-3xl overflow-hidden">
          {/* pc title begin */}
          <div className="hidden md:block text-xl mt-0 mb-7 text-center">
            <div className="inline-block z-10 relative">
              {authPage?.title}
              <div className="absolute -bottom-[5px] -left-[40px]">
                <img src="/images/title-border.png" className=" h-[40px]" />
              </div>
            </div>
          </div>
          {/* pc title end */}
          {/* mobile title begin */}
          <div className={`block md:hidden absolute top-0 left-0 w-full h-full ${styles.loginBg}`}></div>
          <div className="flex md:hidden h-[60px] text-[#0052E7] text-xl font-semibold leading-6 text-center justify-center items-center relative z-10">
            {authPage?.title}
          </div>
          {/* mobile title end */}
          <div className={`${styles.authTabs} bg-white rounded-3xl relative z-10 px-8 pb-8 md:bg-transparent md:rounded-none md:px-0`}>
            <VisibleControl visible={!isMobile && hasWeLinkQrCode}>
              <AuthModeSwitch toogle={setIsScanCodeMode} isQrCodeMode={isScanCodeMode} />
            </VisibleControl>

            <VisibleControl visible={!isShowAuthByWeLinkQrCode} fallback={<AuthByWeLinkQrCode />}>
              <VisibleControl visible={authWayItems?.length > 1} fallback={authWayItems?.[0]?.children}>
                <Tabs centered items={authWayItems} destroyInactiveTabPane />
              </VisibleControl>
            </VisibleControl>
          </div>
          <VisibleControl visible={!configrations?.authPage?.hidden7MoorCsBtn}>
            <div className={styles.customerContainer}>
              <SuspensionDriveBox initPosition={csPostion}>
                <CsButton placement="topRight" trigger="click" />
              </SuspensionDriveBox>
            </div>
          </VisibleControl>
        </div>
        <VisibleControl visible={!!configrations?.authPage?.modal}>
          <NModal modal={{ ...configrations?.authPage?.modal, content: notificationData?.data?.note || '' }} />
        </VisibleControl>
      </Spin>
    );
  }
  return <>获取配置失败</>;
};

export default Auth;
