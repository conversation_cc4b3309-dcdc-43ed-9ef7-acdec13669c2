.title {
  font-size: 24px;
  color: @heading-color;
  letter-spacing: 0;
  text-align: center;
}

.customerContainer {
  display: flex;
  flex-direction: row-reverse;
}

.authTabs {
  :global(.ant-tabs-tab-btn) {
    font-size: 16px;
  }
}

:global(.ant-tabs-top .ant-tabs-nav:before) {
  border-bottom: none;
}

@keyframes glow {
  0% {
    background-position: 0px -100%;
  }
  50% {
    background-position: 200% 50%;
  }
  100% {
    background-position: 0px 100%;
  }
}

.loginBg {
  background: linear-gradient(45deg, #00d8ff 0%, #5200ff 100%) 0% 0% / 200% 200%;
  background-repeat: no-repeat;
  animation: glow 10s ease infinite alternate;
  filter: blur(69px);
  will-change: transform;
}
