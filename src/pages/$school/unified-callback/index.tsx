import { connectByUnified } from '@/api/portal';
import { useAuthSuccessHandler } from '@/hooks/useAuthSuccessHandler';
import useConfigration from '@/hooks/useConfigration';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useUrlQueries from '@/hooks/useUrlQuires';
import Auth from '@/pages/$school/auth/index';
import { getDeviceType, saveToken } from '@/utils';
import { Button, notification, Spin } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import OverConncurrecyModal, { useOverConncurrecyModal } from '../auth/components/OverConncurrecyModal';
import errorHandler, { commonErrorHandler } from '../auth/_error-handler';

const deviceType = getDeviceType({});
const UnifiedCallback: React.FC = () => {
  const configrations = useConfigration();
  const urlQuires = useUrlQueries();
  const overConncurrecyModalRef = useOverConncurrecyModal();
  const [getRedirectUrl] = useRedirectUrl();
  const handleSuccess = useAuthSuccessHandler();
  const [showRetryButton, setShowRetryButton] = useState(false);
  const [showAuthComponent, setShowAuthComponent] = useState(false);
  const [loading, setLoading] = useState(false);
  const unifiedAuthConfig = useMemo(() => {
    const authWays = configrations?.authPage?.authWays;
    return authWays?.find((item) => item.type === 'unified') as AuthByUnified;
  }, [configrations]);
  // 处理超并发
  const handleOverConcurrency = () => {
    overConncurrecyModalRef.current?.show();
  };

  const connect = async (code: string, appType: string) => {
    try {
      setLoading(true);
      const { data } = await connectByUnified({
        appType,
        code,
        deviceType,
        redirectUrl: getRedirectUrl(),
      });
      if (data && data.token) {
        saveToken(data.token);
      }
      if (data && data.statusCode === 200) {
        handleSuccess();
      } else if (data) {
        const { statusCode, error } = data;
        if (errorHandler[statusCode] && errorHandler[statusCode][error]) {
          errorHandler[statusCode][error].call(null, data, () => {}, {
            onOverConcurrency: handleOverConcurrency,
            configrations: configrations,
          });
        } else {
          commonErrorHandler(data);
        }
        setShowAuthComponent(true);
      }
    } catch (error) {
      notification.error({
        message: '身份过期，请重新登录',
      });
      setShowAuthComponent(true);
    } finally {
      setLoading(false);
    }
  };

  const handleOfflineSuccess = () => {
    setShowRetryButton(true);
  };

  const handleRetry = () => {
    const unifiedUrl = unifiedAuthConfig?.unifiedUrl;
    if (!unifiedUrl) {
      notification.error({
        message: '统一身份认证地址未配置',
      });
      return;
    }
    window.location.href = unifiedUrl;
  };

  useEffect(() => {
    console.log(urlQuires);
    const code = urlQuires.code || urlQuires.ticket;
    if (code && urlQuires.appType) {
      connect(code, urlQuires.appType);
    }
  }, [urlQuires.code, urlQuires.ticket, urlQuires.appType]);
  return (
    <div>
      {loading && (
        <div className="w-full mt-4 flex justify-center">
          <Spin spinning />
        </div>
      )}
      {showAuthComponent ? <Auth /> : <></>}
      <OverConncurrecyModal
        modalTitle={configrations?.authPage?.concurrentModal?.title}
        ref={overConncurrecyModalRef}
        onOfflineSuccess={handleOfflineSuccess}
        footer={
          showRetryButton ? (
            <Button type="primary" onClick={handleRetry}>
              重试
            </Button>
          ) : (
            <></>
          )
        }
      />
    </div>
  );
};

export default UnifiedCallback;
