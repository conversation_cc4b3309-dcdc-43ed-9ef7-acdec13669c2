/**
 * <AUTHOR>
 */

import { Button, Form, Input, Modal } from 'antd';
import React, { useState } from 'react';

interface AddMacModalProps {
  onOk: (params: object) => Promise<void>;
  loading: boolean;
}

const AddMacModal: React.FC<AddMacModalProps> = (props) => {
  const regMac = /^([a-zA-Z0-9]{2}){6}$/;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState<boolean>(false);

  /** 点击按钮，模态框打开 */
  const handleClick = () => {
    setVisible(true);
  };

  /** 模态框关闭 */
  const handleCancel = () => {
    form.resetFields();
    setVisible(false);
  };

  /** 确认新增无感知设备 */
  const handleOk = () => {
    form.validateFields().then((d) => {
      props
        .onOk(d)
        .then(() => {
          handleCancel();
        })
        .catch(() => {
          handleCancel();
        });
    });
  };

  return (
    <div style={{ textAlign: 'center', marginBottom: 18 }}>
      <Button onClick={handleClick} type="link">
        新增无感知绑定
      </Button>
      <Modal
        open={visible}
        onCancel={handleCancel}
        onOk={handleOk}
        title="新增绑定"
        confirmLoading={props.loading}
        destroyOnClose
        okText="新增"
        cancelText="取消"
        maskClosable={false}
      >
        <Form form={form} wrapperCol={{ span: 20 }} labelCol={{ span: 4 }} validateTrigger="onBlur">
          <Form.Item
            label="mac地址"
            name="mac"
            rules={[
              { required: true, message: '请输入mac地址' },
              { pattern: regMac, message: '请输入正确的mac地址(12位不需要包含“-”或“:”)' },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="密码" name="password" rules={[{ required: true, message: '请输入密码' }]}>
            <Input.Password />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AddMacModal;
