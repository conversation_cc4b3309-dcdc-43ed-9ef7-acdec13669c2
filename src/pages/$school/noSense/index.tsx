import { deleteMac, getMacList, saveMac } from '@/api/portal';
import { DeleteMacParams, getMacParam, saveMacParam } from '@/api/portalData';
import { getUserSessionInfo } from '@/api/radacct';
import Back from '@/components/Back';
import NICon from '@/components/NIcon';
import Title from '@/components/Title';
import useConfigration from '@/hooks/useConfigration';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import useUsernameForCheck from '@/hooks/useUsernameForCheck';
import { getDeviceType } from '@/utils';
import { Helmet, useMutation, useQuery } from '@umijs/max';
import { List, message } from 'antd';
import { get } from 'lodash';
import React from 'react';
import AddMacModal from './AddMacModal';
import styles from './index.less';

const NoSensePanel: React.FC = () => {
  /** 获取无感列表 */
  const [, , { getIp }] = useRedirectUrl();
  const userIp = getIp();
  const connectInfoQuery = useQuery(['getUserSessionInfo'], () => getUserSessionInfo({ userIp }));
  const connectInfo = connectInfoQuery.data?.data;
  const mac = get(connectInfo, 'mac') || '';
  const username = get(connectInfo, 'username');
  const [usernameForCheck] = useUsernameForCheck();
  const configrations = useConfigration();
  const tabletIsPc = configrations?.systemConfig?.tabletIsPc;
  const devicetypeUaFirst = configrations?.systemConfig?.devicetypeUaFirst;
  const macListQuery = useQuery(
    ['getMacList', mac, username, usernameForCheck],
    () => {
      const deviceType = getDeviceType({
        tabletIsPc,
        devicetypeUaFirst,
      });
      const param: getMacParam = {
        mac,
        username: username || usernameForCheck || '',
        deviceType,
      };
      return getMacList(param);
    },
    {
      enabled: !!(mac && (username || usernameForCheck)),
    }
  );

  const macList = macListQuery?.data?.data ? macListQuery.data.data : [];

  const saveMacMutation = useMutation(
    ['saveMac'],
    (params: saveMacParam) => {
      return saveMac(params);
    },
    {
      onSuccess: () => {
        message.success('MAC地址绑定成功');
        macListQuery.refetch();
      },
    }
  );

  const deleteMacMutation = useMutation(
    ['deleteMac'],
    (params: object) => {
      return deleteMac(params as DeleteMacParams);
    },
    {
      onSuccess: () => {
        message.success('MAC地址解绑成功');
        macListQuery.refetch();
      },
    }
  );

  const unBindMac = (v: string) => {
    deleteMacMutation.mutate({ mac: v, username: username || useUsernameForCheck });
  };

  const handleAddMac = async (data: object) => {
    const params: object = {
      deviceType: getDeviceType({
        tabletIsPc,
        devicetypeUaFirst,
      }),
      username,
      ...data,
    };
    await saveMacMutation.mutateAsync(params as saveMacParam);
    return;
  };

  /** 具体界面 */
  return (
    <div className="w-full px-8 md:px-0 py-8 md:py-0">
      <Helmet>
        <title>无感知绑定记录</title>
      </Helmet>
      <Title>无感知绑定记录</Title>
      <List
        style={{ width: '100%', height: 380 }}
        className={styles.middle}
        locale={{ emptyText: '无在线设备' }}
        dataSource={macList}
        renderItem={({ mac: macItem }) => (
          <List.Item>
            <div className={styles.name}>
              <p>{macItem}</p>
              <div style={{ cursor: 'pointer' }} onClick={() => unBindMac(macItem)} aria-hidden>
                <NICon type="icon-jiebang" style={{ marginRight: 6, paddingTop: 2 }} />
                <span className={styles.offLine}>解绑</span>
              </div>
            </div>
          </List.Item>
        )}
      />
      <div className={styles.footer}>
        <AddMacModal onOk={handleAddMac} loading={saveMacMutation.isLoading} />
        <Back />
      </div>
    </div>
  );
};

export default NoSensePanel;
