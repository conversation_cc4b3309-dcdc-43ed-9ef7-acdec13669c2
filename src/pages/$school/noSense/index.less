.title {
  font-size: 16px;
  color: @heading-color;
  margin-bottom: 64px;
  font-weight: bold;
}

.middle {
  flex: 1;
  overflow-y: auto;
  width: 100%;
  .name {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    p {
      font-family: @font-Medium;
      font-size: 16px;
      color: @text-color;
      letter-spacing: 0;
      margin: 0px;
    }
    .offLine {
      font-size: 16px;
      letter-spacing: 0;
    }
  }
}

.footer {
  margin-bottom: 0px;
  width: 100%;
  .msg {
    font-family: @font-Regular;
    font-size: @font-size-base;
    color: @text-color-secondary;
    margin-bottom: 24px;
    text-align: center;
    text-decoration: underline;
    cursor: pointer;
  }
  .button {
    width: 100%;
    height: 50px;
    border-radius: @border-radius-base;
  }
}
