import Back from '@/components/Back';
import HtmlWrapper from '@/components/HtmlWrapper';
import useConfigration from '@/hooks/useConfigration';
import { Helmet } from '@umijs/max';

const PrivacyPolicy = () => {
  const configrations = useConfigration();
  const privacyPolicyPage = configrations?.privacyPolicyPage;
  let html = privacyPolicyPage?.html;
  let browserTitle = privacyPolicyPage?.browserTitle;
  if (configrations) {
    return (
      <div className="w-full px-8 md:px-0 py-8 md:py-0">
        <Helmet>
          <title>{browserTitle}</title>
        </Helmet>
        <HtmlWrapper html={html} />
        <Back />
      </div>
    );
  }
};
export default PrivacyPolicy;
