import HtmlWrapper from '@/components/HtmlWrapper';
import useConfigration from '@/hooks/useConfigration';
import { Helmet, useParams } from '@umijs/max';
import { get } from 'lodash';

const Repair = () => {
  const configrations = useConfigration();
  const params = useParams();
  const id = Number(get(params, 'id', ''));
  const activityPages = get(configrations, 'activityPages', []);
  let html = '';
  let browserTitle = '';
  for (let i = 0; i < activityPages.length; i++) {
    if (activityPages[i].id === id) {
      html = activityPages[i].html;
      browserTitle = activityPages[i].browserTitle;
      break;
    }
  }
  if (configrations) {
    return (
      <div className="pt-4">
        <Helmet>
          <title>{browserTitle}</title>
        </Helmet>
        <HtmlWrapper html={html} />
      </div>
    );
  }
};
export default Repair;
