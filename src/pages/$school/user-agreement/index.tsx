import HtmlWrapper from '@/components/HtmlWrapper';
import useConfigration from '@/hooks/useConfigration';
import { Helmet } from '@umijs/max';

const UserAgreement = () => {
  const configrations = useConfigration();
  const userAgreementPage = configrations?.userAgreementPage;
  let html = userAgreementPage?.html;
  let browserTitle = userAgreementPage?.browserTitle;
  if (configrations) {
    return (
      <div className="w-full px-8 md:px-0 py-8 md:py-0">
        <Helmet>
          <title>{browserTitle}</title>
        </Helmet>
        <HtmlWrapper html={html} />
      </div>
    );
  }
};
export default UserAgreement;
