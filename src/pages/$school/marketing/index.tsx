import useConfigration from '@/hooks/useConfigration';
import React, { useEffect, useRef } from 'react';

const Marketing: React.FC = () => {
  const configrations = useConfigration();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const marketingPageUrl = configrations?.systemConfig?.marketingPageUrl;
  useEffect(() => {
    if (iframeRef.current) {
      // 高度为宽度两倍
      iframeRef.current.height = (iframeRef.current.offsetWidth * 896) / 414 + 'px';
    }
  }, [iframeRef.current]);
  return (
    <div>
      <iframe ref={iframeRef} style={{ border: 'none', width: '100%' }} src={marketingPageUrl}></iframe>
    </div>
  );
};

export default Marketing;
