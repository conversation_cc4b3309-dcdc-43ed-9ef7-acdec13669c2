/* eslint-disable react-hooks/rules-of-hooks */
import { Button, Form, Input, message } from 'antd';
import React, { useState } from 'react';
import styles from './index.less';

const FormItem = Form.Item;
interface PasswordProps {
  password: string;
  confirm: string;
}
interface CompleteInfoProps {
  onOk: (password: string, confirm: string) => void;
}
const CompleteInfo: React.FC<CompleteInfoProps> = (props) => {
  const { onOk } = props;
  const [form] = Form.useForm();
  const [btnDisable, setBtnDisable] = useState(true);

  const handleSubmit = (values: PasswordProps) => {
    const reg = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{6,16}$/g;
    if (!reg.test(values.password)) {
      message.warn('密码不符合规则');
      return;
    }
    if (values.confirm !== values.password) {
      message.warn('两次密码输入不一致');
      return;
    }
    onOk?.(values.password, values.confirm);
  };

  const handleInputChange = () => {
    const { password, confirm } = form.getFieldsValue();
    if (password && confirm) {
      setBtnDisable(false);
    } else {
      setBtnDisable(true);
    }
  };

  return (
    <div>
      <Form onFinish={handleSubmit} form={form}>
        <FormItem
          name="password"
          extra={<p className={styles.password__extra}>密码由6-16位英文字母、数字组成</p>}
          rules={[
            {
              required: true,
              whitespace: true,
              message: '请设置新密码',
            },
          ]}
        >
          <Input.Password onChange={handleInputChange} type="password" placeholder="设置新密码" allowClear className={styles.passwordInput} />
        </FormItem>
        <FormItem
          name="confirm"
          rules={[
            {
              required: true,
              whitespace: true,
              message: '确认密码',
            },
          ]}
        >
          <Input.Password onChange={handleInputChange} type="password" placeholder="确认密码" allowClear className={styles.passwordInput} />
        </FormItem>
        <FormItem>
          <Button disabled={btnDisable} htmlType="submit" size="large" block type="primary">
            提交
          </Button>
        </FormItem>
      </Form>
    </div>
  );
};

export default CompleteInfo;
