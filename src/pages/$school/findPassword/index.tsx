import { checkIdNumber, findPwdsByIdnumber, resetPassword, sendVerifyButton, verifyCodeButton } from '@/api/api';
import NFrom from '@/components/NForm';
import TimingButton from '@/components/TimingButton';
import Title from '@/components/Title';
import useConfigration from '@/hooks/useConfigration';
import useUrlSchool from '@/hooks/useUrlSchool';
import { identityCodeValid, joinPrefixOrSuffix } from '@/utils';
import regs from '@/utils/regs';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Helmet, Link, useMutation, useNavigate } from '@umijs/max';
import { Button, Form, Input, message, Tabs } from 'antd';
import { AxiosError } from 'axios';
import React, { useRef, useState } from 'react';
import CompleteInfo from './components/CompleteInfo';
import styles from './index.less';

interface FormValues {
  phone: string;
  type: string;
  verifyCode: string;
}
const FormItem = Form.Item;
const FindPassword: React.FC = () => {
  const formValuesRef = useRef<Record<string, string>>({});
  const [phoneForm] = Form.useForm();
  const [idNumberForm] = Form.useForm();
  const navigate = useNavigate();
  const school = useUrlSchool();
  const configrations = useConfigration();
  const [display, setDisplay] = useState(0);
  const [activeKey, setActiveKey] = useState('phone');
  const [phoneBtnDisable, setPhoneBtnDisable] = useState(true);
  // 是否点击验证身份证号
  const [isCheckIdNumber, setIsCheckIdNumber] = useState(false);
  // 验证身份证号结果
  const [checkIdNumberResult, setCheckIdNumberResult] = useState(false);

  const mutation = useMutation(['sendVerifyButton'], sendVerifyButton, {
    onSuccess() {
      // console.log(data);
    },
    onError(error: AxiosError) {
      if (error?.response?.data?.code === 1024) {
        message.error('该手机号码未注册');
      } else {
        message.error('验证码发送失败');
      }
    },
  });

  const resetPasswordMutation = useMutation(['resetPassword'], resetPassword, {
    onSuccess() {
      navigate(`/${school}/findSuccess`, {
        replace: true,
      });
    },
  });

  const findPassowrdPage = configrations?.findPassowrdPage;
  const prependForm = findPassowrdPage?.validate?.prependForm || [];
  const phoneNextStepMutation = useMutation(['verifyCodeButton'], verifyCodeButton, {
    onSuccess(data) {
      if (data.data === true) {
        setDisplay(1);
      } else {
        message.error('请输入正确的手机号或者验证码');
      }
    },
  });

  // 手机号表单-input变化
  const handlePhoneInputChange = () => {
    const { phone, verifyCode } = phoneForm.getFieldsValue();
    if (phone && verifyCode) {
      setPhoneBtnDisable(false);
    } else {
      setPhoneBtnDisable(true);
    }
  };

  // 发送验证码
  const sendVerify = () => {
    let values = phoneForm.getFieldsValue();
    let phone = values.phone;
    if (!regs.phone.test(phone)) {
      message.warn('手机号码格式不正确');
      return false;
    }
    const validate = findPassowrdPage?.validate;
    if (validate) {
      phone = joinPrefixOrSuffix(
        phone,
        {
          prefixType: validate.mobilePrefixType,
          prefixValue: validate.mobilePrefixValue,
          suffixType: validate.mobileSuffixType,
          suffixValue: validate.mobileSuffixValue,
        },
        values
      );
    }
    return mutation.mutateAsync({
      phone,
      type: 'RETRIEVE',
    });
  };

  // 手机号-下一步
  const handleSubmitPhoneForm = (values: FormValues) => {
    let phone = values.phone;
    const validate = findPassowrdPage?.validate;
    if (validate) {
      phone = joinPrefixOrSuffix(
        values.phone,
        {
          prefixType: validate.mobilePrefixType,
          prefixValue: validate.mobilePrefixValue,
          suffixType: validate.mobileSuffixType,
          suffixValue: validate.mobileSuffixValue,
        },
        values
      );
    }
    phoneNextStepMutation.mutate({
      phone,
      type: 'RETRIEVE',
      verifyCode: values.verifyCode,
    });
    formValuesRef.current.phone = phone;
    formValuesRef.current.verifyCode = values.verifyCode;
  };

  // 提交手机号表单
  const handleResetPasswordByPhoneOk = (password: string, confirm: string) => {
    const values = formValuesRef.current;
    let phone = values.phone;
    const validate = findPassowrdPage?.validate;
    if (validate) {
      phone = joinPrefixOrSuffix(
        values.phone,
        {
          prefixType: validate.mobilePrefixType,
          prefixValue: validate.mobilePrefixValue,
          suffixType: validate.mobileSuffixType,
          suffixValue: validate.mobileSuffixValue,
        },
        values
      );
    }
    resetPasswordMutation.mutate({
      password,
      confirm,
      user: phone,
      verifyCode: values.verifyCode,
    });
  };

  // 前端验证身份证号格式
  const checkIdNumberValidator = (rule: any, value: string) => {
    if (value && identityCodeValid(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject('请输入真实的身份证号');
    }
  };

  // 请求验证账号-身份证号是否匹配
  const handleCheckIdNumber = () => {
    idNumberForm
      .validateFields(['user', 'identification'])
      .then((values) => {
        const { user, identification } = values;

        checkIdNumber({
          user: `${configrations?.telexNumber}${user}`,
          identification: identification?.toUpperCase(),
        })
          .then((result) => {
            setIsCheckIdNumber(true);
            setCheckIdNumberResult(result.data);

            if (result.data) {
              message.success('验证成功');
              formValuesRef.current.user = `${configrations?.telexNumber}${user}`;
              formValuesRef.current.identification = identification?.toUpperCase();
            } else {
              message.error('验证失败');
            }
          })
          .catch((error) => {
            if (error?.response?.data?.code === 1024) {
              message.error('该手机号码未注册');
            }
          });
      })
      .catch((err: any) => {
        console.log(err);
      });
  };

  // 身份证号-点击下一步
  const handleSubmitIdNumberForm = () => {
    setDisplay(1);
  };

  // 提交身份证号表单
  const handleResetPasswordByIdNumberOk = (password: string, confirm: string) => {
    const values = formValuesRef.current;

    findPwdsByIdnumber({
      user: `${values?.user}`,
      identification: values?.identification,
      password,
      confirm,
    }).then(() => {
      navigate(`/${school}/findSuccess`, {
        replace: true,
      });
    });
  };

  const items = [
    {
      label: '手机找回',
      key: 'phone',
      children: (
        <Form onFinish={handleSubmitPhoneForm} form={phoneForm} validateTrigger="onBlur">
          {prependForm?.map((item, idx) => (
            <NFrom authFormItem={item} key={idx} />
          ))}
          <FormItem
            name="phone"
            rules={[
              {
                required: true,
                message: '请输入手机号',
              },
              {
                pattern: regs.phone,
                message: '手机号格式不正确',
              },
            ]}
          >
            <Input onChange={handlePhoneInputChange} placeholder="请输入手机号" size="large" allowClear maxLength={11} />
          </FormItem>
          <FormItem
            name="verifyCode"
            rules={[
              {
                required: true,
                message: '请输入验证码',
              },
              {
                pattern: regs.onlyNumber,
                message: '验证码格式不正确',
              },
            ]}
          >
            <Input
              type="text"
              maxLength={6}
              placeholder="请输入验证码"
              allowClear
              size="large"
              onChange={handlePhoneInputChange}
              addonAfter={
                <TimingButton
                  style={{ width: '102px', margin: '0 -11px', padding: 0, border: 0, height: 38 }}
                  textAfterNumber=""
                  size="large"
                  min={0}
                  max={60}
                  type="primary"
                  block
                  onClick={sendVerify}
                  loading={mutation.isLoading}
                >
                  发送验证码
                </TimingButton>
              }
            />
          </FormItem>
          <FormItem>
            <Button disabled={phoneBtnDisable} type="primary" htmlType="submit" size="large" block>
              下一步
            </Button>
          </FormItem>
        </Form>
      ),
    },
    {
      label: '身份证号找回',
      key: 'idNumber',
      children: (
        <Form onFinish={handleSubmitIdNumberForm} form={idNumberForm} validateTrigger="onBlur">
          {prependForm?.map((item, idx) => (
            <NFrom authFormItem={item} key={idx} />
          ))}
          <FormItem
            name="user"
            getValueFromEvent={(event) => event.target.value.replace(/\s+/g, '')}
            rules={[{ required: true, whitespace: true, message: '请输入账号' }]}
          >
            <Input
              size="large"
              allowClear
              placeholder="请输入账号"
              onChange={() => {
                setIsCheckIdNumber(false);
              }}
            />
          </FormItem>
          <FormItem name="identification" rules={[{ validator: checkIdNumberValidator }]}>
            <Input
              size="large"
              allowClear
              onChange={() => {
                setIsCheckIdNumber(false);
              }}
              addonAfter={
                <Button
                  type="primary"
                  style={{ width: '102px', margin: '0 -11px', padding: 0, border: 0, height: 38 }}
                  size="large"
                  onClick={handleCheckIdNumber}
                  icon={isCheckIdNumber && (checkIdNumberResult ? <CheckCircleOutlined /> : <CloseCircleOutlined />)}
                >
                  验证
                </Button>
              }
              placeholder="请输入身份证号"
            />
          </FormItem>
          <FormItem>
            <Button disabled={!(isCheckIdNumber && checkIdNumberResult)} type="primary" htmlType="submit" size="large" block>
              下一步
            </Button>
          </FormItem>
        </Form>
      ),
    },
  ];

  return (
    <div className="w-full px-8 md:px-0 py-8 md:py-0">
      <Helmet>
        <title>找回密码</title>
      </Helmet>
      <Title>{display === 0 ? '找回密码' : '重置密码'}</Title>
      <div className="h-4"></div>
      {display === 0 ? (
        <Tabs centered items={items} activeKey={activeKey} defaultActiveKey="phone" onChange={(activeKey) => setActiveKey(activeKey)} />
      ) : (
        <CompleteInfo onOk={activeKey === 'phone' ? handleResetPasswordByPhoneOk : handleResetPasswordByIdNumberOk} />
      )}
      <div className={styles.verifyFooter}>
        <Link to={`/${school}/auth`}>
          <span className={styles.login}>返回连接</span>
        </Link>
      </div>
    </div>
  );
};

export default FindPassword;
