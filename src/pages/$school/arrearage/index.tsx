import { bindMobile, sendBindMobileCode } from '@/api/portal';
import HtmlWrapper from '@/components/HtmlWrapper';
import ProgressBar from '@/components/ProgressBar';
import TimingButton from '@/components/TimingButton';
import { useCheckLastExpiredPlanIsMarkUp, useMobileBinded } from '@/hooks/queries';
import { useAuthSuccessHandler } from '@/hooks/useAuthSuccessHandler';
import useConfigration from '@/hooks/useConfigration';
import useLocalStorage from '@/hooks/useLocalStorage';
import useUrlQueries from '@/hooks/useUrlQuires';
import useUrlSchool from '@/hooks/useUrlSchool';
import { replayRequest } from '@/http/axios';
import { saveToken } from '@/utils';
import { decrypt } from '@/utils/n-crypto';
import regs from '@/utils/regs';
import { Helmet, useMutation } from '@umijs/max';
import { Button, Form, Input, message, Modal, notification } from 'antd';
import { AxiosError } from 'axios';
import { useEffect, useState } from 'react';

const ArrearagePanel = () => {
  const configrations = useConfigration();
  const school = useUrlSchool();
  const [form] = Form.useForm();
  const { eu, ep } = useUrlQueries();
  const username = eu ? decrypt(eu) : '';
  const password = ep ? decrypt(ep) : '';
  const [bindMobileModalOpen, setBindMobileModalOpen] = useState(false);
  const [receiveFreeOrderModalOpen, setReceiveFreeOrderModalOpen] = useState(false);
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const textList = ['领取套餐中', '套餐生效中', '登录中'];
  const handleSuccess = useAuthSuccessHandler();
  const [mobileBinded] = useMobileBinded(username);
  const arrearagePage = configrations?.arrearagePage;
  const [{ webAuthUser }] = useLocalStorage<{ webAuthUser?: string }>('webAuthUserForCheckMarkUp', {});
  const [isMarkUp] = useCheckLastExpiredPlanIsMarkUp(webAuthUser);

  useEffect(() => {
    if (!mobileBinded) {
      setBindMobileModalOpen(true);
    }
  }, [mobileBinded]);
  const sendVerifyCodeMutation = useMutation(['sendVerifyCode'], sendBindMobileCode, {
    onError(err: AxiosError) {
      if (err?.response?.data?.code === 1026) {
        notification.error({
          message: '用户已存在，请联系客服解决',
        });
      } else {
        notification.error({
          message: '发送失败',
        });
      }
    },
  });

  const bindMobileMutation = useMutation(['bindMobile'], bindMobile, {
    onSuccess() {
      setBindMobileModalOpen(false);
      notification.success({
        message: '已绑定手机号',
      });
      setReceiveFreeOrderModalOpen(true);
    },
    onError(error) {
      if (error?.response?.data?.code === 1024) {
        message.error('用户名或密码错误');
      }
    },
  });

  // 帮助用户重新拨号
  useEffect(() => {
    if (receiveFreeOrderModalOpen) {
      const textInterval = setInterval(() => {
        setCurrentTextIndex((prevIndex) => (prevIndex + 1) % textList.length);
      }, 3000);

      // 10s 后重拨，共重拨3次
      let count = 0;
      const requestInterval = setInterval(async () => {
        count += 1;
        await replayRequest('connect').then(({ data }) => {
          if (data && data.token) {
            saveToken(data.token);
          }
          if (data && data.statusCode === 200) {
            notification.success({
              message: '领取成功',
            });
            clearInterval(textInterval);
            clearInterval(requestInterval);
            handleSuccess();
          } else if (count === 3) {
            // 无手机号用户当前无帐期 && 手机权益送帐期已领且已过期，进入此页绑定手机号后，领取免费套餐必失败，3次失败后跳转至/arrearage页
            if (data?.errorDescription?.indexOf('No billing plan subscription, please subscribe') > -1) {
              notification.error({
                message: configrations?.authPage?.messages?.noBillingPlan || '当前账号未订购套餐，请订购套餐',
              });
              window.location.href = `/${school}/arrearage`;
            } else {
              notification.error({
                message: data.errorDescription,
              });
              window.location.href = `/${school}/auth`;
            }
          }
        });
      }, 10 * 1000);

      return () => {
        clearInterval(textInterval);
        clearInterval(requestInterval);
      };
    }
  }, [receiveFreeOrderModalOpen]);

  const sendVerify = async () => {
    const values = await form.validateFields([['mobile']]);
    const mobile = values.mobile;
    if (mobile) {
      return sendVerifyCodeMutation.mutateAsync({
        mobile,
      });
    }
  };

  const handleFormFinish = async (values: Record<string, string>) => {
    if (password && username) {
      bindMobileMutation.mutate({
        code: values.verifyCode,
        mobile: values.mobile,
        password,
        username,
      });
    }
  };
  return (
    <div className="min-h-[480px] flex flex-col justify-between">
      <Helmet>
        <title>{arrearagePage?.browserTitle}</title>
      </Helmet>
      {arrearagePage?.html && !isMarkUp ? (
        <HtmlWrapper html={arrearagePage?.html} />
      ) : arrearagePage?.markUpHtml && isMarkUp ? (
        <HtmlWrapper html={arrearagePage?.markUpHtml} />
      ) : (
        <div className="mt-8">
          <div className="text-xl text-center mb-16 font-bold">
            你没有可用套餐 ，
            <br />
            关注公众号进行套餐购买
          </div>
          <div className="flex flex-1 items-center justify-between flex-col">
            <img src="/arrearage/QRcode.png" alt="ORcode" className="w-[198px] mb-6" />
            <div className="text-sm text-gray-400 text-center m-0">
              关注微信服务号
              <br />
              了解你的用网情况
            </div>
          </div>
        </div>
      )}

      <Modal open={bindMobileModalOpen} title="温馨提示" destroyOnClose maskClosable={false} closable={false} footer={null} centered>
        <div>
          <p className="font-bold">{arrearagePage?.bindMobileTip}</p>
        </div>
        <Form form={form} onFinish={handleFormFinish} layout="vertical">
          <Form.Item
            name="mobile"
            rules={[
              {
                required: true,
                message: '请输入手机号',
              },
              {
                pattern: regs.phone,
                message: '手机号格式不正确',
              },
            ]}
          >
            <Input placeholder="请输入手机号" size="large" allowClear maxLength={11} />
          </Form.Item>
          <Form.Item
            name="verifyCode"
            rules={[
              {
                required: true,
                message: '请输入验证码',
              },
              {
                pattern: regs.onlyNumber,
                message: '验证码格式不正确',
              },
            ]}
          >
            <Input
              type="text"
              maxLength={6}
              placeholder="请输入验证码"
              allowClear
              size="large"
              addonAfter={
                <TimingButton
                  style={{ width: '102px', margin: '0 -11px', padding: 0, border: 0, height: 38 }}
                  textAfterNumber=""
                  size="large"
                  min={0}
                  max={60}
                  type="primary"
                  block
                  onClick={sendVerify}
                  loading={sendVerifyCodeMutation.isLoading}
                >
                  发送验证码
                </TimingButton>
              }
            />
          </Form.Item>
          <Button htmlType="submit" type="primary" block loading={bindMobileMutation.isLoading}>
            提交
          </Button>
        </Form>
      </Modal>

      <Modal
        title="正在为您领取免费体验套餐，请稍候~"
        centered
        maskClosable={false}
        closable={true}
        footer={null}
        bodyStyle={{ padding: '30px 24px 40px' }}
        open={receiveFreeOrderModalOpen}
        onCancel={() => setReceiveFreeOrderModalOpen(false)}
      >
        <p>{textList[currentTextIndex]}</p>
        <ProgressBar width="100%" height="24px" />
      </Modal>
    </div>
  );
};

export default ArrearagePanel;
