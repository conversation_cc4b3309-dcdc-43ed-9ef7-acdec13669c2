import { getToken, parseUrlToObj } from '@/utils';
import { decrypt, encrypt } from '@/utils/n-crypto';
import { message } from 'antd';
import type { AxiosError, InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';
import { get } from 'lodash';
import getErrorMsg from './error';

export type CustomAxiosRequestConfig = InternalAxiosRequestConfig & {
  // 是否可以重复发送
  repeatable: boolean;
  // 需要重庆发送请求的 id,
  requestId: string;
};

interface RequestHistoryType extends InternalAxiosRequestConfig {
  id: string;
}

// 记录需要重复发送的请求
const requestHistory: RequestHistoryType[] = [];

const disableErrorTipApiUrl = ['/portal-conversion/api/v3/session/mult/dproxy/account'];

const enableCrypto = true;

function logOnDevOrDebug(...params: any): void {
  // @ts-ignore
  if (process.env.NODE_ENV === 'development' || window.debugmode) {
    console.log(...params);
  }
}

// 拦截get请求，做加密，只需要处理url
export function getHandler({ config }: { config: InternalAxiosRequestConfig }): InternalAxiosRequestConfig {
  const c = config;
  // 拿到问号之前的url，和问号之后的query
  const [urlWithoutQuery, queryUrl] = config?.url?.split('?') || [];
  // 先用一个对象存储query
  let fullQueryObj: Record<string, any> = {};
  // 如果有问号，就把问号之后的query转换成对象
  if (queryUrl) {
    const queries = parseUrlToObj(`?${queryUrl}`);
    fullQueryObj = { ...queries };
  }
  // 如果有config.params，就把config.params转换成对象
  if (config?.params) {
    fullQueryObj = { ...fullQueryObj, ...config.params };
    c.params = {}; // 把config.params置空，否则会拼接到url后面
  }
  if (Object.keys(fullQueryObj).length === 0) {
    // 如果没有query，就不处理
    return c;
  }
  logOnDevOrDebug(c.url, '加密前的query', fullQueryObj);
  const encryptRes = encrypt(fullQueryObj);
  logOnDevOrDebug(c.url, '加密后的query', encryptRes);
  c.url = `${urlWithoutQuery}?params=${encryptRes}`;
  return c;
}

export function postHandler({ config }: { config: InternalAxiosRequestConfig }): InternalAxiosRequestConfig {
  // url的处理和get一样
  const c = getHandler({ config });
  if (c.data) {
    // 如果有data，就做加密处理
    if (c.data instanceof FormData) {
      // 如果是formdata，就把formdata转换成对象
      const formData = c.data as FormData;
      const formDataObj: Record<string, any> = {};
      formData.forEach((value, key) => {
        formDataObj[key] = value;
      });
      const newFormData = new FormData();
      const encryptRes = encrypt(formDataObj);
      if (encryptRes) {
        newFormData.append('params', encryptRes);
        logOnDevOrDebug(c.url, '加密前的formdata', formDataObj);
        logOnDevOrDebug(c.url, '加密后的formdata', encryptRes);
      }
      c.data = newFormData;
    } else {
      // 如果不是formdata，就直接做加密处理
      const dataCopied = JSON.parse(JSON.stringify(c.data)); // 深拷贝一份data，否则会影响到原始的data
      const encryptRes = encrypt(c.data);
      if (encryptRes) {
        c.data = encryptRes;
        // @ts-ignore
        c.headers['Content-Type'] = 'application/json;charset=UTF-8';
        logOnDevOrDebug(c.url, '加密前的body', dataCopied);
        logOnDevOrDebug(c.url, '加密后的body', encryptRes);
      }
    }
  }
  return c;
}

const decryptResponseData = (response: AxiosResponse) => {
  const { data, ...rest } = response;
  if (enableCrypto && typeof data === 'string') {
    const decryptJsonStr = decrypt(data);
    if (decryptJsonStr) {
      try {
        const decryptData = JSON.parse(decryptJsonStr);
        logOnDevOrDebug(response.config.url, '解密后的body', decryptData);
        return {
          ...rest,
          data: decryptData,
        };
      } catch (err: any) {
        console.log(err);
      }
    }
  }
  return response;
};

const decryptResponseError = (err: AxiosError) => {
  if (err && err.response) {
    const encryptData = err.response.data;
    const decryptErr: AxiosError = { ...err };
    if (encryptData && typeof encryptData === 'string') {
      const decryptJsonStr = decrypt(encryptData);
      if (decryptJsonStr) {
        try {
          const decryptData = JSON.parse(decryptJsonStr);
          // decryptErr.data = decryptData;
          if (decryptErr.response) {
            decryptErr.response.data = decryptData;
          }
          logOnDevOrDebug(err.request.responseURL, '解密后的err body', decryptData);
        } catch (e: any) {
          console.log(e);
        }
      }
    }
    return Promise.reject(decryptErr);
  }
  return Promise.reject(err);
};

axios.interceptors.request.use((config) => {
  const newConfig = config;
  const token = getToken();
  newConfig.params = { ...config.params };
  if (token) {
    newConfig.headers.Authorization = token;
  }
  return newConfig;
});
axios.interceptors.request.use((config) => {
  {
    const isNoEnc = String(config.headers?.['x-safe-level']) === '0'; // 有这个header就不加密
    if (!enableCrypto || isNoEnc) {
      return config;
    }
    const method = config.method?.toLocaleLowerCase();
    if (method === 'get' || method === 'delete') {
      return getHandler({ config });
    }
    if (method === 'post' || method === 'put') {
      return postHandler({ config });
    }
    return config;
  }
});

// 请求拦截器：记录请求
axios.interceptors.request.use((config) => {
  // @ts-ignore
  if (config?.repeatable) {
    // 记录请求信息（不包含重复调用的 _requestId）
    requestHistory.push({
      // @ts-ignore
      id: config?.requestId,
      url: config.url,
      method: config.method,
      headers: config.headers,
      data: config.data,
      params: config.params,
    });
  }

  return config;
});

axios.interceptors.response.use(decryptResponseData, decryptResponseError);
axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error && error.response) {
      const url = error?.config?.url || '';
      const code = get(error, 'response.data.code', '');
      const msg = getErrorMsg(code);
      if (disableErrorTipApiUrl.includes(url)) {
        return Promise.reject(error);
      }
      if (msg) {
        message.error(msg);
        error.message = msg;
      }
      return Promise.reject(error);
    }
    return Promise.reject(error);
  }
);

export const axiosPost = async <Res>(url: string, data: object | string, config?: InternalAxiosRequestConfig) => {
  const encryptStr = encrypt(data);
  // @ts-ignore
  if (window.debugmode || process.env.NODE_ENV === 'development') {
    console.log(`--- start --------------${url}-----------------`);
    if (data) {
      console.log('request body: ', data);
    }
  }
  const res = await axios.create().post<string>(url, encryptStr, {
    ...config,
    headers: {
      'Content-Type': 'application/json',
    },
    transformRequest: [(d) => d],
  });
  if (res.data) {
    const resJson = decrypt(res.data);
    if (resJson) {
      const resObj: Res = JSON.parse(resJson);
      // @ts-ignore
      if (window.debugmode || process.env.NODE_ENV === 'development') {
        if (resObj) {
          console.log('请求结果: ', resObj);
        }
        console.log(`--- end --------------${res.config.url}-----------------`);
        console.log('');
      }
      return resObj;
    }
  }
  return null;
};

// 重放接口
export const replayRequest = (requestId: string) => {
  // 在历史记录中找到对应请求
  const originalRequest = requestHistory.find((req) => req.id === requestId);

  if (!originalRequest) {
    return Promise.reject(`该重放接口未缓存：${requestId}`);
  }

  // 使用原始请求信息重新发送请求
  return axios({
    url: originalRequest.url,
    method: originalRequest.method,
    headers: originalRequest.headers,
    data: originalRequest.data,
    params: originalRequest.params,
  });
};

export default axios;
