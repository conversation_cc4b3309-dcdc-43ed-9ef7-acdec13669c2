const errorCode: { [index: number]: string } = {
  400: '',
  403: '',
  500: '',
  1000: '断开连接失败',
  1001: '未知 nasIp',
  1002: '用户不存在',
  1003: '缺少 X-Account-Id 控制属性',
  1004: '读取用户信息错误',
  1005: '读取用户mac错误',
  1006: '非法手机号码',
  1007: '电话号码已体验',
  1008: '手机号码已注册',
  1009: '验证码错误',
  1010: '用户未注册',
  1011: '写入用户信息错误',
  1012: 'Mac 不存在',
  1013: '别名太长',
  1014: 'json 到对象错误',
  1015: '对象到 json 错误',
  1016: '生成令牌错误',
  1017: '令牌无效',
  1018: '',
  1019: '',
  1020: '读取用户计划错误',
  1021: '产品不存在错误',
  1022: '找不到用户名',
  // 1023: '旧密码错误',
  9001: '未知错误',
  10000: '用户没有操作权限',
  9002: '弹性搜索错误',
  9003: 'MAC被其他用户绑定',
};

const getErrorMsg = (code: number) => errorCode[code];
export default getErrorMsg;
