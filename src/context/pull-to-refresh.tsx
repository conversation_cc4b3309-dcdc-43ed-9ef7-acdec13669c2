import React, { createContext, useState } from 'react';

export declare type PullStatus = 'pulling' | 'canRelease' | 'refreshing' | 'complete';

interface PullContexType {
  pullStatus: PullStatus;
  set: (status: PullStatus) => void;
}
export const PullContext = createContext<PullContexType>({
  pullStatus: 'complete',
  set: () => {},
});

const { Provider, Consumer: PullConsumer } = PullContext;

const ContextProvider: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  const [pullStatus, setPullStatus] = useState<PullStatus>('complete');
  const initValue = {
    pullStatus,
    set: setPullStatus,
  };
  return <Provider value={initValue}>{children}</Provider>;
};

export { PullConsumer };

export default ContextProvider;
