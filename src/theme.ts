const theme = {
  'font-Regular': `PingFangSC-Regular, 'Microsoft YaHei', 'Helvetica Neue', Helvetica,
  'Nimbus Sans L', <PERSON>l, 'Liberation Sans', 'Hiragino Sans GB', 'Source Han Sans CN Normal',
  'Wenquanyi Micro Hei', 'WenQuanYi Zen Hei', 'ST Heiti', SimHei, 'WenQuanYi Zen Hei Sharp',
  sans-serif`,

  'font-Medium': `PingFangSC-Medium, 'Microsoft YaHei', 'Helvetica Neue', Helvetica,
  'Nimbus Sans L', Arial, 'Liberation Sans', 'Hiragino Sans GB', 'Source Han Sans CN Normal',
  'Wenquanyi Micro Hei', 'WenQuanYi Zen Hei', 'ST Heiti', SimHei, 'WenQuanYi Zen Hei Sharp',
  sans-serif`,

  'font-Semibold': `PingFangSC-Semibold, 'Microsoft YaHei', 'Helvetica Neue', Helvetica,
  'Nimbus Sans L', Arial, 'Liberation Sans', 'Hiragino Sans GB', 'Source Han Sans CN Normal',
  'Wenquanyi Micro Hei', 'WenQuanYi Zen Hei', 'ST Heiti', SimHei, 'WenQuanYi Zen Hei Sharp',
  sans-serif`,

  'font-family': `-apple-system, BlinkMacSystemFont,'Segoe UI','PingFang SC','Hiragino Sans GB',
  'Microsoft YaHei','Helvetica Neue',Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol'`,

  '@box-shadow-large': '0 2px 6px 0 rgba(0,0,0,0.10)',
  'error-color': '#f5222d', // 错误色
  'font-size-base': '14px',
  'heading-color': 'rgba(0, 0, 0, 0.85)', // 标题色
  'text-color': 'rgba(0, 0, 0, 0.65)', // 主文本色
  'text-color-secondary': 'rgba(0, 0, 0, 0.45)', // 次文本色
  'disabled-color': 'rgba(0, 0, 0, 0.25)',
  'border-radius-base': '2px', // 组件/浮层圆角
  'border-color-base': '#d9d9d9', // 边框色
  'box-shadow-base': '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
};

export default theme;
