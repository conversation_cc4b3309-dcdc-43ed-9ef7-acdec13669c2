import React from 'react';

interface LinkBoxProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

const LinkBox: React.FC<LinkBoxProps> = (props) => {
  const { className = '', style = {}, children } = props;
  return (
    // Grid 布局，每行两列，两端对齐
    <div className={`grid grid-cols-2 justify-between gap-y-2 ${className}`} style={style}>
      {children}
    </div>
  );
};

export default LinkBox;
