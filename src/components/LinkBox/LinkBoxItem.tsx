import { Link } from '@umijs/max';
import React from 'react';

interface LinkBoxItemProps {
  link: string;
  bold?: boolean;
  blank?: boolean;
  children?: React.ReactNode;
}
const LinkBoxItem: React.FC<LinkBoxItemProps> = (props) => {
  const { link, bold, blank, children } = props;
  if (blank) {
    return (
      <a href={link} className={`w-fit opacity-85 text-sm text-[#008eff] leading-5 block ${bold ? 'font-bold' : ''}`} target="_blank" rel="noreferrer">
        {children}
      </a>
    )
  }
  return (
    <Link to={link} className={`w-fit opacity-85 text-sm text-[#008eff] leading-5 block ${bold ? 'font-bold' : ''}`}>
      {children}
    </Link>
  )
}

export default LinkBoxItem;
