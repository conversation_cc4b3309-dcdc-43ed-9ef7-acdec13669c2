import useConfigration from '@/hooks/useConfigration';
import useQRCode from '@/hooks/useQRCode';
import useRedirectUrl from '@/hooks/useRedirectUrl';
import { Popover } from 'antd';
import { TooltipPlacement } from 'antd/es/tooltip';
import React from 'react';
import styles from './index.less';

interface CsButtonProps {
  placement?: TooltipPlacement;
  trigger?: string;
}

const key1 = encodeURIComponent('来源');
const key2 = encodeURIComponent('所在学校');
const key3 = encodeURIComponent('同学');

const CsButton: React.FC<CsButtonProps> = ({ placement = 'top', trigger = 'hover' }) => {
  const configrations = useConfigration();
  const [, , { getMac }] = useRedirectUrl();
  const userMac = getMac();

  const encodedSchoolName = encodeURIComponent(configrations?.schoolName || '');

  const [canvasRef] = useQRCode(
    `https://ykf-webchat02.7moor.com/wapchat.html?accessId=7fe1aa40-daa7-11e9-937f-294f7640b27f&fromUrl=wechat.com.cn&urlTitle=portal-web&customField={"${key1}":"portal-web","${key2}":"${encodedSchoolName}","user_labels":{"school":"${encodedSchoolName}","name":""}}&otherParams={"nickName":"${key3}"}&clientId=${userMac
      .replace(/[:-]/g, '')
      .toUpperCase()}`,
    {
      width: 200,
      errorCorrectionLevel: 'L',
    }
  );

  return (
    <Popover
      content={
        <div className="flex flex-col items-center">
          <p className="text-center my-3">请使用微信扫描二维码联系客服~</p>
          <div className="relative">
            <canvas ref={canvasRef}></canvas>
            <img src="/customer.png" className={styles.qrcodeIcon} />
          </div>
        </div>
      }
      trigger={trigger}
      placement={placement}
    >
      <div className={styles.customerBox}>
        <img src="/customer.png" />
        客服
      </div>
    </Popover>
  );
};

export default CsButton;
