/* eslint-disable consistent-return */
import useConfigration from '@/hooks/useConfigration';
import axios from '@/http/axios';
import { KeyOutlined, UserOutlined } from '@ant-design/icons';
import { Form, Input, Radio, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import NIcon from '../NIcon';

interface NFromProps {
  authFormItem: AuthForm;
  onInputBlur?: (name: string) => void;
}

const AntdIconMap: Record<string, React.FC<any>> = {
  UserOutlined: UserOutlined,
  KeyOutlined: KeyOutlined,
};

function getAntdIcon(str?: string, color?: string) {
  if (!str || !AntdIconMap[str]) {
    return null;
  }
  const style = { color: color || 'inherit' };
  const Ic = AntdIconMap[str];
  return <Ic style={style} />;
}

function getIcon(str?: string, color?: string) {
  if (!str) {
    return <></>;
  }
  return getAntdIcon(str, color) || <NIcon style={{ color: color || 'inherit' }} type={str} />;
}

const NFrom: React.FC<NFromProps> = (props) => {
  const { authFormItem, onInputBlur } = props;
  const initialState = useConfigration();
  const primaryColor = initialState?.theme?.primaryColor;
  const [selectOptions, setSelectOptions] = useState<any[]>([]);

  const handleInputBlur = (name: string) => {
    if (onInputBlur) {
      onInputBlur(name);
    }
  };

  const getSelectOptions = async () => {
    if (authFormItem.type === 'select') {
      const { useRemoteOptions, options = [], remoteOptionsConfig } = authFormItem;
      if (useRemoteOptions && remoteOptionsConfig) {
        const { data } = await axios.get(remoteOptionsConfig.url);
        const listPath = remoteOptionsConfig.listPath;
        // 从路径中获取数据
        const list = listPath.reduce((prev, cur) => {
          return prev[cur];
        }, data);
        // 给没有value的数据添加value
        const adaptList = list.map((item: any) => {
          const { valueName, uniqueKey } = remoteOptionsConfig;
          const newItem = { ...item };
          if (!newItem[valueName]) {
            newItem[valueName] = newItem[uniqueKey] + '#';
          }
          return newItem;
        });
        setSelectOptions(adaptList);
      } else {
        setSelectOptions(options);
      }
    }
  };

  useEffect(() => {
    getSelectOptions();
  }, [authFormItem]);

  if (authFormItem.type === 'input') {
    const reg = new RegExp(authFormItem.inputRegex || '');
    const inputErrorTip = authFormItem.inputErrorTip || '';
    return (
      <Form.Item
        rules={[
          { required: authFormItem.required, message: authFormItem.emptyTip },
          { pattern: reg, message: inputErrorTip },
        ]}
        name={authFormItem.name}
        key={authFormItem.name}
      >
        <Input
          prefix={getIcon(authFormItem.icon, primaryColor)}
          size="large"
          placeholder={authFormItem.placeholder}
          onBlur={() => handleInputBlur(authFormItem.name)}
        />
      </Form.Item>
    );
  }
  if (authFormItem.type === 'select') {
    const { searchable = true, remoteOptionsConfig } = authFormItem;
    const labelName = remoteOptionsConfig?.labelName || 'label';
    const valueName = remoteOptionsConfig?.valueName || 'value';
    const noDataTip = authFormItem.noDataTip || '无数据';
    return (
      <Form.Item
        rules={[{ required: authFormItem.required, message: authFormItem.emptyTip }]}
        name={authFormItem.name}
        key={authFormItem.name}
        initialValue={authFormItem.default}
      >
        <Select
          size="large"
          placeholder={authFormItem.placeholder}
          showSearch={searchable}
          optionFilterProp={labelName}
          options={selectOptions}
          fieldNames={{ label: labelName, value: valueName }}
          allowClear
          notFoundContent={noDataTip}
          style={{
            borderRadius: '10px',
          }}
        ></Select>
      </Form.Item>
    );
  }
  if (authFormItem.type === 'radio') {
    const { options } = authFormItem;
    return (
      <Form.Item
        rules={[{ required: authFormItem.required, message: authFormItem.emptyTip }]}
        name={authFormItem.name}
        key={authFormItem.name}
        initialValue={authFormItem.default}
      >
        <Radio.Group size="large" options={options}></Radio.Group>
      </Form.Item>
    );
  }
  if (authFormItem.type === 'password') {
    return (
      <Form.Item rules={[{ required: authFormItem.required, message: authFormItem.emptyTip }]} name={authFormItem.name} key={authFormItem.name}>
        <Input.Password prefix={getIcon(authFormItem.icon, primaryColor)} size="large" placeholder={authFormItem.placeholder} />
      </Form.Item>
    );
  }
  return <>获取配置失败</>;
};
export default NFrom;
