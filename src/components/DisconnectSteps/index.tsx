import { Steps, StepsProps } from 'antd';
import React from 'react';

type DisconnectStepsProps = StepsProps;
const DisconnectSteps: React.FC<DisconnectStepsProps> = (props) => {
  return (
    <div className="mt-4">
      <Steps direction="horizontal" responsive={false} size="small" items={[{ title: '开始' }, { title: '身份验证' }, { title: '断开连接' }]} {...props} />
    </div>
  );
};

export default DisconnectSteps;
