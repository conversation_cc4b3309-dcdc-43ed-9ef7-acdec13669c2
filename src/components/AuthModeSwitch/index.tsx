import { DesktopOutlined, QrcodeOutlined } from '@ant-design/icons';
import React from 'react';

interface AuthModeSwitchProps {
  isQrCodeMode: boolean;
  toogle: (qrCodeMode: boolean) => void;
}

const triAngleStyle: React.CSSProperties = {
  borderWidth: 24,
  borderStyle: 'solid',
  borderColor: 'transparent transparent #fff #fff',
  left: 0,
  bottom: 0,
};
const AuthModeSwitch: React.FC<AuthModeSwitchProps> = (props) => {
  const { isQrCodeMode, toogle } = props;
  const handleClick = () => {
    toogle?.(!isQrCodeMode);
  };
  return (
    <div className="absolute cursor-pointer top-0 -right-8 w-fit h-fit bg-white" onClick={handleClick}>
      <div className="text-5xl">{isQrCodeMode ? <DesktopOutlined /> : <QrcodeOutlined />}</div>
      <div style={triAngleStyle} className="absolute"></div>
    </div>
  );
};

export default AuthModeSwitch;
