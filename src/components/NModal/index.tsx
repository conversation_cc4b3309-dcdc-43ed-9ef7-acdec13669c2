/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react/no-danger */
import HtmlWrapper from '@/components/HtmlWrapper';
import { Button, Space } from 'antd';
import jsCookie from 'js-cookie';
import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import TimingButton from '../TimingButton';
import VisibleControl from '../VisibleControl';

interface ModalConfig {
  content: string;
  title?: string;
  closeIcon?: boolean;
  okButtonText?: string;
  closeCountDown?: number;
}

interface NModalProps {
  modal: ModalConfig;
}

const NModal: React.FC<NModalProps> = (props) => {
  const [visible, setVisible] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const [modalPostion, setModalPostion] = useState<{ top: number }>();

  const getTopPosition = () => {
    const authForm = document.getElementById('authForm');

    if (authForm) {
      const rect = authForm.getBoundingClientRect();
      console.log(rect.top);
      setModalPostion({
        top: rect.top,
      });
    }
  };

  useEffect(() => {
    getTopPosition();
    const tid = setTimeout(() => {
      if (!visible) {
        if (Number(jsCookie.get('currentPopUpCount')) >= Number(jsCookie.get('popUpCountEverySession'))) {
          setVisible(false);
        } else {
          setVisible(true);
          let count = Number(jsCookie.get('currentPopUpCount'));
          count += 1;
          jsCookie.set('currentPopUpCount', count.toString());
        }
      }
    }, 1000);
    return () => {
      clearTimeout(tid);
    };
  }, []);

  const { modal } = props;
  const { content, title, closeIcon, okButtonText, closeCountDown = 10 } = modal;

  const handleCancel = () => {
    const footerElement = document.getElementById('footer-contact');

    if (!modalRef.current || !footerElement) {
      setVisible(false);
      return;
    }

    setIsClosing(true);

    // 获取模态框和footer的位置信息
    const modalRect = modalRef.current.getBoundingClientRect();
    const footerRect = footerElement.getBoundingClientRect();

    // 计算需要移动的距离
    const translateX = footerRect.left - modalRect.left + (footerRect.width - modalRect.width) / 2;
    const translateY = footerRect.top - modalRect.top;
    const scale = (footerRect.width / modalRect.width) * 0.2;

    // 应用动画
    modalRef.current.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    modalRef.current.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`;
    modalRef.current.style.opacity = '0';

    // 动画结束后关闭
    setTimeout(() => {
      setVisible(false);
      setIsClosing(false);
      // 重置样式
      if (modalRef.current) {
        modalRef.current.style.transform = '';
        modalRef.current.style.opacity = '';
      }
    }, 300);
  };

  const handleMaskClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleCancel();
    }
  };

  if (!visible) {
    return null;
  }

  const modalContent = (
    <div
      className={`fixed inset-0 bg-black/45 flex items-center justify-center z-[1000] animate-fadeIn ${isClosing ? 'animate-fadeOut' : ''}`}
      onClick={handleMaskClick}
    >
      <div
        ref={modalRef}
        className={`
          bg-white rounded-lg shadow-lg 
          min-w-[32px] w-[90vw] 
          md:w-auto md:min-w-[520px] md:max-w-[520px]
          mx-auto
          h-[548px]
          overflow-y-auto
          animate-slideIn
          transition-all duration-300 ease-in-out
          transform translate-y-0 scale-100 opacity-100
          backface-hidden
          absolute
        `}
        style={{
          maxHeight: 600,
          minHeight: 400,
          top: modalPostion?.top,
        }}
      >
        {/* Header */}
        {(title || closeIcon) && (
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            {title && <div className="text-[rgba(0,0,0,0.88)] font-semibold text-base leading-6">{title}</div>}
            {closeIcon && (
              <button
                className="absolute top-4 right-4 p-0 text-[rgba(0,0,0,0.45)] hover:text-[rgba(0,0,0,0.88)] 
                          font-semibold leading-none bg-transparent border-0 outline-none cursor-pointer 
                          transition-colors duration-300 text-base"
                onClick={handleCancel}
                aria-label="Close"
              >
                ×
              </button>
            )}
          </div>
        )}

        {/* Content */}
        <div className="p-6 text-sm leading-[1.5715] break-words overflow-y-auto">
          <HtmlWrapper className="w-full h-full overflow-y-auto" html={content} />
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-end items-center sticky bottom-0 bg-white">
          <Space size={14} className="w-full justify-center">
            <VisibleControl visible={!!closeCountDown}>
              <span className="text-[rgba(0,0,0,0.45)]">
                倒计时
                <TimingButton immidate isOnlyShowNumber={true} isSetEndTimeToStart={false} max={closeCountDown} min={1} onTimeEnd={handleCancel} />
                秒关闭
              </span>
            </VisibleControl>
            <VisibleControl visible={!!okButtonText}>
              <Button className="min-w-[80px]" type="primary" onClick={handleCancel}>
                {okButtonText}
              </Button>
            </VisibleControl>
          </Space>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default NModal;
