.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modalContainer {
  background: white;
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08);
  min-width: 32px;
  max-width: calc(100vw - 32px);
  margin: 0 auto;
  position: relative;
  top: 100px;
  animation: slideIn 0.3s ease-out;
}
.NModal {
  max-width: 520px;
  margin: 0px auto;
  :global(.ant-modal-content) {
    border-radius: 8px;
    max-height: 80%;
  }
}

.modalHeader {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modalTitle {
  margin: 0;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
}

.closeButton {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 0;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 600;
  line-height: 1;
  text-decoration: none;
  background: transparent;
  border: 0;
  outline: 0;
  cursor: pointer;
  transition: color 0.3s;
  font-size: 16px;

  &:hover {
    color: rgba(0, 0, 0, 0.88);
  }
}

.modalBody {
  padding: 24px;
  font-size: 14px;
  line-height: 1.5715;
  word-wrap: break-word;
  overflow-y: auto;
}

.modalFooter {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.timingText {
  color: rgba(0, 0, 0, 0.45);
}

.okBtn {
  min-width: 80px;
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-40px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.closing {
  animation: fadeOut 0.3s ease-out;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

// 最小化动画相关
.modalContainer {
  backface-visibility: hidden;
  transform: translate(0, 0) scale(1);
  opacity: 1;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
