/* eslint-disable react/jsx-props-no-spreading */
import useInterval from '@/hooks/useInterval';
import { Button, ButtonProps } from 'antd';
import React, { useState } from 'react';

export interface TimingButtonProps extends ButtonProps {
  /**
   * 是否立即执行
   */
  immidate?: boolean;
  /**
   * @description 计时中的大数
   * @default 1
   */
  max?: number;
  /**
   * @description 计时中的小数
   * @default 60
   */
  min?: number;
  /**
   * @description 是否倒计时
   * @default true
   */
  isCountingDown?: boolean;
  /**
   * @description 计时数字前的文字
   * @default ''
   */
  textBeforeNumber?: string;
  /**
   * @description 计时数字后的文字
   * @default ''
   */
  textAfterNumber?: string;
  /**
   * @description 是否只返回数字，默认返回 Button 组件
   * @default false
   */
  isOnlyShowNumber?: boolean;
  /**
   * @description 倒计时结束后，是否将结束时间重置为开始时间
   * @default true
   */
  isSetEndTimeToStart?: boolean;
  /**
   * @description 点击事件，当返回值为 false 时不进入计时状态
   */
  onClick?: (event: React.MouseEvent<HTMLElement, MouseEvent>) => boolean | Promise<any>;
  /**
   * @description 计时结束后执行该事件
   */
  onTimeEnd?: () => void;
}

const TimingButton: React.FC<TimingButtonProps> = (props) => {
  const {
    children,
    max = 60,
    min = 1,
    immidate = false,
    isCountingDown = true,
    onClick,
    textBeforeNumber = '',
    textAfterNumber = '',
    isOnlyShowNumber = false,
    isSetEndTimeToStart = true,
    onTimeEnd,
    ...rest
  } = props;
  const start = isCountingDown ? max : min;
  const end = isCountingDown ? min : max;
  const [countingNumber, setCountingNumber] = useState<number>(start);
  const [isExecute, setIsExecute] = useState<boolean>(immidate);
  const counter = {
    countup() {
      if (countingNumber >= end) {
        setIsExecute(false);
        if (isSetEndTimeToStart) {
          setCountingNumber(start);
        }
        onTimeEnd?.();
        return;
      }
      setCountingNumber(countingNumber + 1);
    },
    countdown() {
      if (countingNumber <= end) {
        setIsExecute(false);
        if (isSetEndTimeToStart) {
          setCountingNumber(start);
        }
        onTimeEnd?.();
        return;
      }
      setCountingNumber(countingNumber - 1);
    },
  };

  useInterval(counter[isCountingDown ? 'countdown' : 'countup'], 1000, isExecute);

  const handleClick = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {
    if (onClick) {
      const re = onClick(event);
      if (typeof re === 'boolean') {
        if (re === false) {
          setIsExecute(false);
        } else {
          setIsExecute(true);
        }
        return;
      }
      re.then(() => setIsExecute(true)).catch(() => setIsExecute(false));
    }
    // setIsExecute(true);
  };

  return (
    <>
      {isOnlyShowNumber ? (
        countingNumber
      ) : (
        <Button disabled={isExecute} onClick={handleClick} {...rest}>
          {isExecute ? `${textBeforeNumber}${countingNumber}${textAfterNumber}` : children}
        </Button>
      )}
    </>
  );
};

export default TimingButton;
