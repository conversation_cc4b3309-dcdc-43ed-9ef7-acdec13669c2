import React from 'react';

interface TipWithIconProps {
  icon?: React.ReactNode;
  text?: React.ReactNode;
}
const TipWithIcon: React.FC<TipWithIconProps> = (props) => {
  const { icon = '', text = '' } = props;
  return (
    <div className="flex gap-x-3 items-center">
      <span className="text-orange-500 text-2xl">{icon}</span>
      <span className="font-semibold">{text}</span>
    </div>
  );
};

export default TipWithIcon;
