import { useNavigate } from '@umijs/max';
import { Button } from 'antd';
import React from 'react';

const Back: React.FC = () => {
  const navigate = useNavigate();
  const handleClick = () => {
    // 如果有上一页则返回上一页，否则返回首页
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };
  return (
    <Button size="large" type="primary" block onClick={handleClick}>
      返回
    </Button>
  );
};

export default Back;
