import HtmlWrapper from '@/components/HtmlWrapper';
import { message } from 'antd';
import copy from 'copy-to-clipboard';
import { get } from 'lodash';
import React from 'react';
import styles from './index.less';
import useConfigration from '@/hooks/useConfigration';

const PublicAccount: React.FC = () => {
  const configrations = useConfigration();
  const findSuccessPage = get(configrations, 'findSuccessPage', {});
  const copyText = get(findSuccessPage, 'copyText', '');
  const isCopy = get(findSuccessPage, 'isCopy', false);
  const handleClick = () => {
    if (isCopy) {
      copy(copyText);
      message.success('复制成功');
    }
  };

  return (
    <div>
      {configrations?.findSuccessPage.html ? (
        <div>
          <HtmlWrapper html={configrations?.findSuccessPage.html} />
          <p className={styles.copy} onClick={handleClick}>
            复制公众号帐号
          </p>
        </div>
      ) : (
        ''
      )}
    </div>
  );
};

export default PublicAccount;
