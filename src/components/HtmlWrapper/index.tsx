import React from 'react';

interface HtmlWrapperProps {
  html?: string;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

const HtmlWrapper: React.FC<HtmlWrapperProps> = (props) => {
  const { children, html = '', style = {}, className = '' } = props;
  return (
    <div className={`braft-output-content ${className}`} style={style} dangerouslySetInnerHTML={{ __html: html }}>
      {children}
    </div>
  );
};

export default HtmlWrapper;
