import useDrag from '@/hooks/useDragableBox';
import React, { useEffect, useRef } from 'react';
import styles from './index.less';

interface SuspensionDriveBoxType {
  children: React.ReactNode;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
  initPosition?: {
    top?: number;
    left?: number;
    right?: number;
    bottom?: number;
  };
}

const SuspensionDriveBox: React.FC<SuspensionDriveBoxType> = ({ maxWidth, children, onClick, initPosition = { bottom: 32, right: 32 } }) => {
  const draggableBoxRef = useRef<HTMLDivElement>(null);

  // 用户是否使用过拖拽功能
  const [isUseTouchMove, setIsUseTouchMove] = React.useState(false);
  // 图标是否收起
  const [isHidden, setIsHidden] = React.useState(false);
  // 滚动条位置
  const [scrollPosition, setScrollPosition] = React.useState(0);
  let timer: any = 0;

  const [translatePosition, { setTranslate }] = useDrag(draggableBoxRef, {
    onTouchStart() {
      setIsUseTouchMove(true);
    },
    onTouchEnd() {
      setIsUseTouchMove(true);
      setIsHidden(false);
    },
  });

  const isScrollEnd = () => {
    const element = draggableBoxRef.current;
    if (scrollPosition === document.body.scrollTop && element) {
      setIsHidden(false);
      setTranslate({ x: 0 });
      element.style.transform = `translate(${translatePosition.x}px, ${translatePosition.y}px)`;
      element.style.opacity = '1';
    }
  };

  const onScroll = () => {
    const element = draggableBoxRef.current;
    if (element && !isUseTouchMove && !isHidden) {
      setIsHidden(true);
      setTranslate({ x: 40 });
      element.style.transform = `translate(${translatePosition.x}px, ${translatePosition.y}px)`;
      element.style.opacity = '0.5';
      clearTimeout(timer);
      timer = setTimeout(isScrollEnd, 1000);
      setScrollPosition(document.body.scrollTop);
    }
  };

  useEffect(() => {
    window.addEventListener('scroll', onScroll);
    return () => window.removeEventListener('scroll', onScroll);
  }, [isUseTouchMove, isHidden]);

  return (
    <div
      ref={draggableBoxRef}
      className={styles.floatBox}
      onClick={onClick}
      style={{
        ...initPosition,
      }}
    >
      {children}
    </div>
  );
};

export default SuspensionDriveBox;
