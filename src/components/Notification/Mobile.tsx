import { getNotification } from '@/api/notification';
import useConfigration from '@/hooks/useConfigration';
import { NotificationFilled } from '@ant-design/icons';
import { useQuery } from '@umijs/max';
import { useState } from 'react';
import HtmlWrapper from '../HtmlWrapper';
import './index.css'; // 引入样式文件

function NotificationMobile() {
  const [isExpanded, setIsExpanded] = useState(false);
  const configrations = useConfigration();
  const modal = configrations?.authPage?.modal;
  const title = modal?.title || '欢迎使用网络！！！';
  const { data } = useQuery(
    ['getNotification-tongzhi'],
    () => {
      if (configrations) {
        const { schoolNameEn } = configrations;
        return getNotification(`${schoolNameEn}-tongzhi`);
      }
    },
    {
      enabled: !!modal,
      retry: false,
      refetchOnWindowFocus: false,
    }
  );
  const notificationContent = data?.data?.note || modal?.content || '';

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  if (!notificationContent) {
    return null;
  }

  return (
    <div className={`notification-bar-wrap `}>
      <div className={`notification-bar ${isExpanded ? 'expanded' : ''}`}>
        <div className={`notification-content ${isExpanded ? 'expanded' : ''}`}>
          {isExpanded ? (
            <HtmlWrapper html={notificationContent} />
          ) : (
            <div>
              <NotificationFilled className="text-blue-500" />
              {title}
            </div>
          )}
        </div>
        <div className={`toggle-button ${isExpanded ? 'expanded' : ''}`} onClick={toggleExpanded}>
          {isExpanded ? '收起' : '点击查看内容'}
        </div>
      </div>
    </div>
  );
}

export default NotificationMobile;
