import { getNotification } from '@/api/notification';
import useConfigration from '@/hooks/useConfigration';
import { useQuery } from '@umijs/max';
import HtmlWrapper from '../HtmlWrapper';
import './index.css'; // 引入样式文件

function NotificationPC() {
  const configrations = useConfigration();
  const modal = configrations?.authPage?.modal;
  const { data } = useQuery(
    ['getNotification-tongzhi'],
    () => {
      if (configrations) {
        const { schoolNameEn } = configrations;
        return getNotification(`${schoolNameEn}-tongzhi`);
      }
    },
    {
      enabled: !!modal,
      retry: false,
      refetchOnWindowFocus: false,
    }
  );
  const notificationContent = data?.data?.note || modal?.content || '';
  if (!notificationContent) {
    return null;
  }
  return (
    <div className="notification-bar-pc-wrap shadow-2xl bg-white rounded-2xl  relative md:w-2/5 md:min-w-[450px] overflow-hidden">
      <div className="w-full px-4 pt-4 absolute top-0 left-0 right-0 bottom-0 overflow-auto">
        <HtmlWrapper html={notificationContent} />
      </div>
    </div>
  );
}

export default NotificationPC;
