.notification-bar-wrap {
  width: 100%;
  background-color: #ffffff;
  height: 48px;
  box-sizing: content-box;
  padding-bottom: 16px;
}

/* 仅在手机端展示 */
@media (min-width: 980px) {
  .notification-bar-wrap {
    display: none;
  }
}

.notification-bar {
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  background-color: #ffffff;
  height: 48px; /* 初始高度 */
  transition: height 0.3s ease; /* 添加过渡效果 */
  width: 90%;
  margin: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.notification-bar.expanded {
  position: fixed;
  height: 80vh; /* 展开时的高度 */
}

.notification-content {
  flex: 1;
  padding: 16px;
  height: 100%;
  overflow: hidden;
}

.notification-content.expanded {
  
  overflow-y: auto;
}

.toggle-button {
  background-color: #007bff;
  color: #ffffff;
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease; /* 添加过渡效果 */
  position: absolute;
  bottom: 8px;
  right: 4px;
}

.toggle-button.expanded {
  bottom: 8px;
}

/* 仅在pc端展示 */
@media (max-width: 980px) {
  .notification-bar-pc-wrap {
    display: none;
  }
}

.notification-bar-wrap-content {
  position: sticky;
  bottom: -33px;
  left: 0;
  width: 100%;
  height: 48px; /* 24像素的高度 */
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)); /* 背景渐变，从透明到白色 */
  pointer-events: none; /* 使其不影响鼠标事件 */
}
