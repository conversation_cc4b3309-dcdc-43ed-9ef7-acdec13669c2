import React from 'react';
import NICon from '../NIcon';
import './index.less';

interface DeviceTypeProps {
  device: string;
  deviceName: string;
}
const deviceMap: {
  [key: string]: string;
} = {
  pc: 'icon-pc',
  pad: 'icon-ipad',
  iphone: 'icon-iphone',
  android: 'icon-android',
  mobile: 'icon-iphone',
  ios: 'icon-iphone',
};
const DeviceType: React.FC<DeviceTypeProps> = (props) => {
  const { device, deviceName } = props;
  const lowerDevice = device?.toLocaleLowerCase();
  const deviceType = deviceMap[lowerDevice] ? deviceMap[lowerDevice] : deviceMap.pc;

  return (
    <span className="device-type">
      <NICon type={deviceType} className="align-middle mr-1" />
      <span className="text-secondary-color text-base">{deviceName}</span>
    </span>
  );
};

export default DeviceType;
