import React from 'react';

interface TitleProps {
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}
const Title: React.FC<TitleProps> = ({ children, className = '', style = {} }) => {
  return (
    <div
      className={` text-2xl font-semibold pt-12 pb-6 mt-0 mb-0 text-center ${className}`}
      style={{
        ...style,
      }}
    >
      {children}
    </div>
  );
};

export default Title;
