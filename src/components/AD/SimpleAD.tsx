import React from 'react';

interface SimpleADProps {
  src?: string;
  content?: string;
  position?: 'UP' | 'DOWN';
}
const SimpleAD: React.FC<SimpleADProps> = (props) => {
  const { src, content, position } = props;
  return (
    <div className="w-full max-w-[300px]">
      {position === 'UP' && content && <div className="text-center text-sm text-gray-500">{content}</div>}
      <img width="100%" className="w-full" src={src} />
      {position === 'DOWN' && content && <div className="text-center text-sm text-gray-500">{content}</div>}
    </div>
  );
};

export default SimpleAD;
