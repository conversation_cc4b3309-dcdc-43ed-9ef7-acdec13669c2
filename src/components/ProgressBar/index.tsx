import { useEffect, useState } from 'react';

const ProgressBar = ({ width = '300px', height = '30px' }) => {
  const [progress, setProgress] = useState(0);
  const totalSteps = 10; // 总步数，可根据实际情况调整
  const colors = ['#c6e5fe', '#afdafd', '#a1d0ff', '#8bc3fb', '#73b4f5', '#5fa9f9', '#4aa2fa', '#3e9dfc', '#3a89f7', '#2e82f7'];

  useEffect(() => {
    const intervalId = setInterval(() => {
      setProgress((prevProgress) => {
        if (prevProgress < totalSteps - 1) {
          return prevProgress + 1;
        } else {
          return 0;
        }
      });
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  return (
    <div style={{ display: 'flex', width, height, backgroundColor: 'transparent', borderRadius: '10px' }}>
      {[...Array(totalSteps)].map((_, index) => (
        <div
          key={index}
          style={{
            width: '30px',
            height: height,
            backgroundColor: index <= progress ? colors[Math.min(index, colors.length - 1)] : '#f0f0f0',
            borderRadius: index === 0 ? '10px 0 0 10px' : index === totalSteps - 1 ? '0 10px 10px 0' : '0',
            marginRight: '2px',
            transition: 'background-color 0.5s ease',
            flexGrow: 1,
          }}
        />
      ))}
    </div>
  );
};

export default ProgressBar;
