import { Button, ButtonProps } from 'antd';
import React from 'react';
interface Props extends ButtonProps {
  onClick?: () => void;
  children?: React.ReactNode;
  loading?: boolean;
}
const CsiicThemeButton: React.FC<Props> = (props) => {
  const { children } = props;

  return (
    <Button
      style={{ backgroundImage: 'linear-gradient(270deg, #62D7FF 0%, #5346FF 100%)' }}
      className="w-full flex justify-center items-center h-11 text-base font-semibold leading-6 text-center text-white rounded-md cursor-pointer hover:text-white active:text-white  focus:text-white"
      {...props}
    >
      <span className="text-white">{children}</span>
    </Button>
  );
};

export default CsiicThemeButton;
