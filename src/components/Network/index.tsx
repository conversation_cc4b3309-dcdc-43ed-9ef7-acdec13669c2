import { UserSessionInfo } from '@/api/radacctData';
import useConfigration from '@/hooks/useConfigration';
import useInterval from '@/hooks/useInterval';
import React, { useState } from 'react';
import VisibleControl from '../VisibleControl';
import styles from './index.less';

interface NetworkProps {
  userSessionInfo?: UserSessionInfo;
}
const Network: React.FC<NetworkProps> = (props) => {
  const configrations = useConfigration();

  const networkInfo = configrations?.successPage?.networkInfo;
  const userSessionInfo = props.userSessionInfo;
  const [acctTime, setAcctTime] = useState('');
  useInterval(() => {
    if (userSessionInfo && userSessionInfo.acctStartTime) {
      const acctStartTime = new Date(userSessionInfo.acctStartTime);
      const acctTime = new Date().getTime() - acctStartTime.getTime();
      const acctTimeStr = `${Math.floor(acctTime / 1000 / 60 / 60)}:${Math.floor((acctTime / 1000 / 60) % 60)}:${Math.floor((acctTime / 1000) % 60)}`;
      setAcctTime(acctTimeStr);
    }
  }, 1000);
  if (!userSessionInfo) {
    return <></>;
  }
  return (
    <div className="w-full flex flex-col gap-y-1 flex-wrap">
      <div className="w-full flex flex-wrap">
        <VisibleControl visible={networkInfo?.username}>
          <div className="pb-2" style={{ minWidth: '50%' }}>
            <span className={styles.itemLabel}>用户名：</span>
            <span className={styles.itemValue}>{userSessionInfo.username}</span>
          </div>
        </VisibleControl>
        <VisibleControl visible={networkInfo?.acctTime}>
          <div className="pb-2">
            <span className={styles.itemLabel}>在线时长：</span>
            <span className={styles.itemValue}>{acctTime}</span>
          </div>
        </VisibleControl>
      </div>
      <div className="w-full flex">
        <VisibleControl visible={networkInfo?.ip}>
          <div className="pb-2 grow">
            <span className={styles.itemLabel}>IP地址：</span>
            <span className={styles.itemValue}>{userSessionInfo.framedIPAddress}</span>
          </div>
        </VisibleControl>
      </div>
    </div>
  );
};

export default Network;
