import React from 'react';

interface UseDragOption {
  onTouchStart?: () => void;
  onTouchMove?: () => void;
  onTouchEnd?: () => void;
}

function useDrag(ref: React.RefObject<HTMLElement>, options?: UseDragOption) {
  const [isDragging, setIsDragging] = React.useState(false);
  const [startX, setStartX] = React.useState(0);
  const [startY, setStartY] = React.useState(0);
  const [translateX, setTranslateX] = React.useState(0);
  const [translateY, setTranslateY] = React.useState(0);

  const onTouchStart = React.useCallback(
    (event: TouchEvent) => {
      setIsDragging(true);
      setStartX(event.touches[0].clientX);
      setStartY(event.touches[0].clientY);
      options?.onTouchStart?.();
    },
    [options]
  );

  const onTouchMove = React.useCallback(
    (event: TouchEvent) => {
      event.preventDefault();
      if (!isDragging) {
        return;
      }
      const deltaX = event.touches[0].clientX - startX;
      const deltaY = event.touches[0].clientY - startY;
      setTranslateX(translateX + deltaX);
      setTranslateY(translateY + deltaY);
      setStartX(event.touches[0].clientX);
      setStartY(event.touches[0].clientY);
      options?.onTouchMove?.();
    },
    [isDragging, startX, startY, translateX, translateY]
  );

  const onTouchEnd = React.useCallback((event: TouchEvent) => {
    setIsDragging(false);
    options?.onTouchEnd?.();
  }, []);

  React.useEffect(() => {
    const element = ref.current;
    if (element) {
      element.addEventListener('touchstart', onTouchStart);
      element.addEventListener('touchmove', onTouchMove);
      element.addEventListener('touchend', onTouchEnd);
    }
    return () => {
      if (element) {
        element.removeEventListener('touchstart', onTouchStart);
        element.removeEventListener('touchmove', onTouchMove);
        element.removeEventListener('touchend', onTouchEnd);
      }
    };
  }, [ref, onTouchStart, onTouchMove, onTouchEnd]);

  React.useEffect(() => {
    const element = ref.current;
    if (element) {
      element.style.transform = `translate(${translateX}px, ${translateY}px)`;
    }
  }, [translateX, translateY]);

  const translatePosition = {
    x: translateX,
    y: translateY,
  };

  const methods = {
    setTranslate({ x, y }: { x?: number; y?: number }) {
      if (typeof x !== 'undefined') {
        setTranslateX(x);
      }
      if (typeof y !== 'undefined') {
        setTranslateY(y);
      }
    },
  };

  return [translatePosition, methods] as [typeof translatePosition, typeof methods];
}

export default useDrag;
