import { useEffect, useRef } from 'react';

const useInterval = (callback: () => void, delay: number, exec = true) => {
  const savedCallback = useRef<() => void>();

  // Remember the latest callback.
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  // Set up the interval.
  useEffect(() => {
    function tick() {
      if (exec) {
        if (savedCallback.current) {
          savedCallback.current();
        }
      }
    }
    if (delay !== null) {
      const id = setInterval(tick, delay);
      return () => clearInterval(id);
    }
    return () => {};
  }, [delay, exec]);
};

export default useInterval;
