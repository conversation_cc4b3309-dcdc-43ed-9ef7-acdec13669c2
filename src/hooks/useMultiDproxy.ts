import { getMultiDproxyAccounts } from '@/api/portal';
import { DproxyAccount } from '@/api/portalData';
import { getIspName } from '@/utils';
import { Form, FormInstance } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import useConfigration from './useConfigration';

function useMultiDproxy(form: FormInstance) {
  const [accountList, setAccountList] = useState<DproxyAccount[]>([]);
  const configration = useConfigration();
  const webAuthUser = Form.useWatch('webAuthUser', form);
  const authModeValue = Form.useWatch('multiDproxy-authMode', form);
  const selectedDproxyAccount = Form.useWatch('multiDproxy-dproxyAccount', form) as string;
  const enableMultiDproxy = configration?.authPage?.enableMultiDproxy;
  const isUserSelectedMultiDproxy = authModeValue === 'isp';

  const getMultiDproxyAccount = () => {
    if (enableMultiDproxy && isUserSelectedMultiDproxy) {
      getMultiDproxyAccounts({ webAuthUser })
        .then((res) => {
          if (res) {
            setAccountList(res);
            if (res.length > 0) {
              // 重新获取列表后，如果之前选择的账期还在列表里面，就不清空，否则清空
              if (!res.find((item) => item.dproxyUsername === form.getFieldValue('multiDproxy-dproxyAccount'))) {
                form.setFieldsValue({
                  'multiDproxy-dproxyAccount': res[0].dproxyUsername,
                });
              }
            } else {
              form.setFieldsValue({
                'multiDproxy-dproxyAccount': undefined,
              });
            }
          }
        })
        .catch(() => {
          setAccountList([]);
          form.setFieldsValue({
            'multiDproxy-dproxyAccount': undefined,
          });
        });
    }
  };

  useEffect(() => {
    getMultiDproxyAccount();
  }, [isUserSelectedMultiDproxy]);

  const authForm = useMemo(() => {
    const enableMultiDproxy = configration?.authPage?.enableMultiDproxy;
    const authFormFormConfig = configration?.authPage?.authForm || [];
    const multiDproxyConfig = configration?.authPage?.multiDproxyConfig;
    // 如果启用了多账期，需要往authForm里面塞一些表单
    if (!enableMultiDproxy) {
      return authFormFormConfig;
    }
    const multiDproxyRadioForm: RadioFormType = {
      type: 'radio',
      isCarry: false,
      name: 'multiDproxy-authMode',
      rememberable: true,
      emptyTip: '请选择上网方式',
      required: true,
      default: 'campus',
      options: [
        { label: multiDproxyConfig?.modeRadio?.doNotUseMultiDproxyLabel || '校园网', value: 'campus' },
        { label: multiDproxyConfig?.modeRadio?.useMultiDproxyLabel || '运营商', value: 'isp' },
      ],
    };

    const multiDproxyAccountForm: SelectFormType = {
      type: 'select',
      isCarry: false,
      name: 'multiDproxy-dproxyAccount',
      rememberable: true,
      emptyTip: multiDproxyConfig?.multiDproxyAccount?.emptyTip || '请选择账期',
      required: true,
      placeholder: multiDproxyConfig?.multiDproxyAccount?.placeholder || '请选择账期',
      searchable: false,
      noDataTip: multiDproxyConfig?.multiDproxyAccount?.noDataTip || '无数据',
      options: accountList.map((item) => ({
        label: `${getIspName(item.dproxyISP)} ${item.dproxyUsername || multiDproxyConfig?.multiDproxyAccount?.unbindTip || '未绑定运营商账号'}`,
        value: item.dproxyUsername,
        disabled: !item.dproxyUsername,
      })),
    };

    const extraFormsForMultiDproxy = [...authFormFormConfig, multiDproxyRadioForm];
    if (isUserSelectedMultiDproxy) {
      extraFormsForMultiDproxy.push(multiDproxyAccountForm);
    }
    return extraFormsForMultiDproxy;
  }, [configration, isUserSelectedMultiDproxy, accountList]);

  return {
    isUserSelectedMultiDproxy,
    refetchDproxyAccount: getMultiDproxyAccount,
    authForm,
    selectedDproxyAccount,
    enableMultiDproxy,
  };
}

export default useMultiDproxy;
