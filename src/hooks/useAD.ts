import { getAd, reportAdEvent } from '@/api/ad';
import { useLocation } from '@umijs/max';
import moment from 'moment';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useUserSessionInfo } from './queries';
import useConfigration from './useConfigration';
import useInterval from './useInterval';
import useRedirectUrl from './useRedirectUrl';
import useUsernameForCheck from './useUsernameForCheck';

/**
 * 为什么这样写？
 * 1. 返回adAttrs，是广告的配置，包括广告图片、广告链接等
 * 2. 返回广告的容器属性，包括onClick事件和ref属性，为了能在任意html盒子上展示广告
 * @returns
 */
function useAD() {
  const adElementRef = useRef<HTMLDivElement>(null);
  const [adAttrs, setAdAttrs] = useState<AdAttrs | null>(null);
  const location = useLocation();
  const [, , { getIp }] = useRedirectUrl();
  const userIp = getIp();
  const [username] = useUsernameForCheck();
  const [userSessionInfo, { isLoading }] = useUserSessionInfo({ userIp, username });
  const configrations = useConfigration();
  const [autoRedirect, setAutoRedirect] = useState(false);

  // 构建广告事件
  const genAdEvent = useCallback(
    (eventType: ADEvents['event']): ADEvents => ({
      uid: userSessionInfo?.uid,
      user_name: userSessionInfo?.username || username,
      channel: 'portal-web',
      ad_id: adAttrs?.adId || '',
      pathname: location.pathname,
      extra: JSON.stringify({ userAgent: navigator.userAgent }),
      event: eventType,
      timestamp: moment().format(),
    }),
    [userSessionInfo, username, adAttrs, location]
  );

  const handleClickAd = async () => {
    if (!adAttrs || adAttrs.type === 'NONE') {
      return;
    }
    const adEvent = genAdEvent('click');
    try {
      reportAdEvent(adEvent); // 上报广告点击事件，但不等待结果
    } catch (error) {
      console.error('广告点击事件上报失败', error);
    }
    // 使用setTimeout，避免广告点击后，页面跳转导致广告点击事件丢失
    setTimeout(() => (window.location.href = adAttrs.targetUrl), 200);
  };
  useInterval(
    () => {
      if (adAttrs?.type === 'AUTO') {
        handleClickAd();
      }
    },
    5000,
    autoRedirect
  );

  useEffect(() => {
    if (!configrations?.systemConfig?.ad || !userSessionInfo?.uid) {
      return;
    }
    getAd(userSessionInfo.uid).then((res) => {
      const attrs = res.data;
      setAdAttrs(attrs);
    });
  }, [configrations, userSessionInfo]);

  useEffect(() => {
    // isLoading作用，在查询完用户信息后才能上报广告浏览事件
    if (adAttrs && adElementRef.current && !isLoading) {
      // 广告出现，上报浏览事件
      const adEvent = genAdEvent('view');
      if (adAttrs.type === 'AUTO') {
        setAutoRedirect(true);
      }
      try {
        reportAdEvent(adEvent);
      } catch (error) {}
    }
  }, [adAttrs, isLoading, genAdEvent]);

  const adContainerProps = {
    ref: adElementRef,
    onClick: handleClickAd,
  };

  return [adAttrs, adContainerProps] as [AdAttrs, typeof adContainerProps];
}

export default useAD;
