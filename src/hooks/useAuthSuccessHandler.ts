import { useNavigate } from '@umijs/max';
import useUrlSchool from './useUrlSchool';
import useConfigration from './useConfigration';
import { useCallback } from 'react';
import { iosRefresh } from '@/utils';

export function useAuthSuccessHandler() {
  const school = useUrlSchool();
  const navigate = useNavigate();
  const configrations = useConfigration();

  const goSuccess = useCallback(() => {
    // if (goMarketingLandpageWhenSucess && marketingPageUrl) {
    //   navigate(`/${school}/marketing`, { replace: true });
    // } else {
    navigate(`/${school}/success`, { replace: true });
    // }
    iosRefresh();
  }, [school, configrations]);

  return goSuccess;
}
