import QRCode, { QRCodeRenderersOptions } from 'qrcode';
import { useEffect, useState } from 'react';
function useQRCode(str: string, options: QRCodeRenderersOptions & { onError?: (error: Error | null | undefined) => void } = {}) {
  const [canvasRef, setCanvasRef] = useState<HTMLCanvasElement | null>(null);
  const handleError = (error: Error | null | undefined) => {
    options?.onError?.(error);
  };
  const generateQRCode = () => {
    if (canvasRef) {
      QRCode.toCanvas(canvasRef, str, { ...options }, handleError);
    }
  };
  useEffect(() => {
    generateQRCode();
  }, [str, canvasRef]);

  return [setCanvasRef];
}

export default useQRCode;
