/* eslint-disable react-hooks/exhaustive-deps */
import get from 'lodash/get';
import { useEffect, useState } from 'react';
import useConfigration from './useConfigration';
/* 通过加载公网部署的js文件判断用户是否有网络
 * js文件加载成功表示用户有网，否则没有网络
 * 公网部署的js文件代码如下：
 * if (onConnectionTestSuccess) {
 *   onConnectionTestSuccess.call();
 * }
 */

interface DetectOption {
  immediately?: boolean;
  onCompleted?: (result: boolean) => void;
  detectDelay?: number;
}

function internalDetect(detectUrl: string, timeout: number) {
  return new Promise<HTMLScriptElement>((resolve, reject) => {
    const oldScript = document.getElementById('detectJs');
    if (oldScript) {
      document.body.removeChild(oldScript);
    }
    const script = document.createElement('script');
    // 添加script标签的src属性，访问链接添加noCache避免缓存
    script.src = `${detectUrl}?noCache=${new Date().getTime()}${Math.floor(Math.random() * 1000000)}`;
    script.setAttribute('id', 'detectJs');
    // 添加script标签的type属性
    script.type = 'text/javascript';
    const timeoutId = setTimeout(() => {
      reject(new Error('超时'));
      clearTimeout(timeoutId);
    }, timeout);
    // 添加script标签加载成功后的回调函数
    // @ts-ignore
    if (script.readyState) {
      // IE
      // @ts-ignore
      script.onreadystatechange = () => {
        // 判断script标签的加载状态是否完成
        // @ts-ignore
        if (script.readyState === 'loaded' || script.readyState === 'complete') {
          // 加载完成后清除回调函数
          // @ts-ignore
          script.onreadystatechange = null;
          resolve(script);
        }
      };
    } else {
      // Others
      script.onload = () => {
        resolve(script);
        clearTimeout(timeoutId);
      };
    }
    // 添加script标签加载失败后的回调函数
    script.onerror = (error) => {
      reject(error);
      clearTimeout(timeoutId);
    };
    // 将创建好的script标签添加到页面上
    document.body.appendChild(script);
  });
}

function useDetectNetWork(option?: DetectOption): [
  boolean,
  boolean,
  () => void,
  {
    isPending: boolean; // 是否还没有探测过
  }
] {
  const [isConnected, setIsConnected] = useState(false);
  const [isPending, setIsPending] = useState(true);
  const [loading, setLoading] = useState(false);
  const configrations = useConfigration();
  const detectUrl = get(configrations, 'systemConfig.detectJsUrl', process.env.DETECT_URL);
  const detectTimeout = Number(get(configrations, 'systemConfig.detectTimeoutMs', process.env.DETECT_TIMEOUT_MS));
  const init = () => {
    setIsPending(false);
    setIsConnected(false);
    setLoading(false);
  };
  const immediately = get(option, 'immediately', true);
  const detectDelay = get(option, 'detectDelay', 0);
  window.onConnectionTestSuccess = () => {
    // 修改网络状态为true，表示用有网络
    setIsConnected(true);
    option?.onCompleted?.(true);
  };
  const detect = () => {
    if (detectUrl) {
      init();
      setLoading(true);
      internalDetect(detectUrl, detectTimeout)
        .then(() => {
          setLoading(false);
        })
        .catch(() => {
          setIsConnected(false);
          option?.onCompleted?.(false);
          setLoading(false);
        });
    }
  };
  useEffect(() => {
    if (immediately) {
      if (detectDelay) {
        setTimeout(() => {
          detect();
        }, detectDelay);
      } else {
        detect();
      }
    }
  }, []);
  return [
    isConnected,
    loading,
    detect,
    {
      isPending,
    },
  ];
}

export default useDetectNetWork;
