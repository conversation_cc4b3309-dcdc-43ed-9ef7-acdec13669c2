import { usernameForCheck } from '@/utils';
import { useEffect, useRef } from 'react';

function useWeLinkQrcode(params: { onGetCode: (code: string) => void; client_id: string; redirect_uri: string }) {
  const qrcodeRef = useRef<HTMLDivElement>(null);
  const handleMessage = function (event: any) {
    let origin = event.origin;
    if (origin === 'https://login.welink.huaweicloud.com') {
      usernameForCheck.remove();
      //判断是否来自WeLink wlLogin扫码事件。 测试环境先注释，上线后需放开
      let loginCode = event.data;
      //拿到loginCode后就可以在这里构造跳转链接进行跳转了
      params?.onGetCode(loginCode);
    }
  };
  useEffect(() => {
    window.addEventListener('message', handleMessage, false);
    return () => {
      window.removeEventListener('message', handleMessage, false);
    };
  }, []);
  useEffect(() => {
    const id = qrcodeRef.current?.getAttribute('id');
    if (id) {
      window.wlQrcodeLogin({
        id,
        client_id: params.client_id,
        redirect_uri: params.redirect_uri,
        response_type: 'code',
        scope: 'snsapi_login',
        style: 'border:none;background-color:#FFFFFF;padding: 0;',
        width: '280',
        height: '280',
        self_redirect: true,
      });
    }
  }, [qrcodeRef.current]);
  return [qrcodeRef] as [typeof qrcodeRef];
}

export default useWeLinkQrcode;
