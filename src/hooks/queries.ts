import { checkLastExpiredPlanIsMarkUp, checkMobileBinded, checkRealname } from '@/api/portal';
import { getUserSessionInfo } from '@/api/radacct';
import { UserSessionInfo } from '@/api/radacctData';
import { useQuery } from '@umijs/max';
import useConfigration from './useConfigration';

interface UseUserSessionOptions {
  onSuccess?: (data: UserSessionInfo) => void;
}

export function useUserSessionInfo({ userIp, username }: { userIp?: string; username?: string }, options?: UseUserSessionOptions) {
  const { data, ...rest } = useQuery(['getUserSessionInfo'], () => getUserSessionInfo({ userIp, username }), {
    onSuccess(data) {
      options?.onSuccess?.(data.data);
    },
  });
  const res = data?.data;
  return [res, rest] as [typeof res, typeof rest];
}

export function useRealnamed() {
  const configration = useConfigration();
  const needCheckRealname = configration?.successPage?.checkRealname || false;
  const { data, ...rest } = useQuery(['getRealnameIpInfo'], checkRealname, {
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: false,
    enabled: needCheckRealname,
  });
  const res = data?.data;
  const isRealnamed = typeof res === 'undefined' ? 'unknow' : res;

  return [isRealnamed, rest] as [typeof isRealnamed, typeof rest];
}

export function useMobileBinded(username?: string) {
  const configration = useConfigration();
  const needCheckMobile = configration?.arrearagePage?.checkMobile || false;
  const { data, ...rest } = useQuery(
    ['getMobileBinded', username],
    () =>
      checkMobileBinded({
        username: username as string,
      }),
    {
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      retry: false,
      enabled: needCheckMobile && !!username,
    }
  );
  const res = data?.data;
  const isMobileBinded = typeof res === 'undefined' ? 'unknow' : res;
  return [isMobileBinded, rest] as [typeof isMobileBinded, typeof rest];
}

// 检查过期套餐是否为加收套餐
export function useCheckLastExpiredPlanIsMarkUp(webAuthUser?: string) {
  const { data, ...rest } = useQuery(
    ['checkLastExpiredPlanIsMarkUp', webAuthUser],
    () =>
      checkLastExpiredPlanIsMarkUp({
        webAuthUser: webAuthUser as string,
      }),
    {
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      retry: false,
      enabled: !!webAuthUser,
    }
  );
  const isMarkUp = data?.data;
  return [isMarkUp, rest] as [typeof isMarkUp, typeof rest];
}
