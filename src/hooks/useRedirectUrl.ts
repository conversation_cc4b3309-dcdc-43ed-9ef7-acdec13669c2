import { getRedirectUrlVale } from '@/utils';
import { decrypt, encrypt } from '@/utils/n-crypto';
import jsCookie from 'js-cookie';
const REDIRECT_URL_KEY = 'redirectUrl';

// 获取、保存认证需要的参数redirectUrl
function useRedirectUrl() {
  const encryptKey = encrypt(REDIRECT_URL_KEY);
  const save = (value?: string) => {
    const unEncryptValue = value || jsCookie.get(REDIRECT_URL_KEY);
    if (unEncryptValue) {
      const encryptValue = encrypt(unEncryptValue);
      if (encryptKey && encryptValue) {
        jsCookie.set(encryptKey, encryptValue);
        jsCookie.remove(REDIRECT_URL_KEY);
        if (getRedirectUrlVale(unEncryptValue)) {
          localStorage.setItem(encryptKey, encryptValue);
        }
      }
    }
  };

  const get = () => {
    let fromCookie = '';
    let fromLocalStorage = '';
    if (encryptKey) {
      fromCookie = jsCookie.get(encryptKey) || '';
      fromLocalStorage = localStorage.getItem(encryptKey) || '';
    }
    const res = getRedirectUrlVale(fromCookie) ? fromCookie : fromLocalStorage;
    if (res) {
      return decrypt(res) || '';
    }
    return '';
  };

  const getIp = () => {
    const redirectUrl = get();
    const userIpReg = /userip=([^&]*)&/;
    const result = redirectUrl.match(userIpReg);
    if (result && result[1]) {
      return result[1];
    }
    return '';
  };

  const getMac = () => {
    const redirectUrl = get();
    const usermacReg = /usermac=([^&]*)&/;
    const result = redirectUrl.match(usermacReg);
    if (result && result[1]) {
      return result[1];
    }
    return '';
  };

  return [get, save, { getIp, getMac }] as [typeof get, typeof save, { getIp: typeof getIp; getMac: typeof getMac }];
}

export default useRedirectUrl;
