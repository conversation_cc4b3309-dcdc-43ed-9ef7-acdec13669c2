import { usernameForCheck } from '@/utils';
import { useState } from 'react';

// 获取用于身份验证的用户名
function useUsernameForCheck(): [string | undefined, (v: string) => void, () => void] {
  const [username, setUsername] = useState(usernameForCheck.get());
  const set = (v: string) => {
    setUsername(v);
    usernameForCheck.set(v);
  };
  const remove = () => {
    setUsername(undefined);
    usernameForCheck.remove();
  };
  return [username, set, remove];
}

export default useUsernameForCheck;
