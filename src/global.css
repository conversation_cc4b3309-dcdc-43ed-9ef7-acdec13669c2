
body {
  background-repeat: no-repeat;
  background-size: cover;
  height: auto;
}
.website-gray {
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  filter: grayscale(100%);
  filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
}
html{
  height: auto;
}
@media (max-width: 767px) {
  body {
    background-image: unset !important;
  }
}
#root {
  height: 100%;
}

.braft-output-content p, .braft-output-content h1, .braft-output-content h2, .braft-output-content h3, .braft-output-content h4, .braft-output-content h5, .braft-output-content h6{
  min-height: 1em;
  margin-bottom: 0;
}

.braft-output-content .image-wrap  {
  text-align: center;
  /* height: auto */
}

.braft-output-content .image-wrap > img {
  width: 100%;
  /* height: auto */
}

.braft-output-content ul,
.braft-output-content ol {
  margin: 16px 0;
  padding: 0
}

.braft-output-content blockquote {
  margin: 0 0 10px 0;
  padding: 15px 20px;
  background-color: #f1f2f3;
  border-left: solid 5px #ccc;
  color: #666;
  font-style: italic
}

.braft-output-content pre {
  max-width: 100%;
  max-height: 100%;
  margin: 10px 0;
  padding: 15px;
  overflow: auto;
  background-color: #f1f2f3;
  border-radius: 3px;
  color: #666;
  font-family: monospace;
  font-size: 14px;
  font-weight: normal;
  line-height: 16px;
  word-wrap: break-word;
  white-space: pre-wrap
}

.braft-output-content pre pre {
  margin: 0;
  padding: 0
}