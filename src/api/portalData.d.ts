export interface ConnectParams {
  deviceType: string;
  redirectUrl: string;
  [key: string]: string;
}

export interface Session {
  acct_session_id: string;
  acct_start_time: string;
  acct_unique_id: string;
  alias: string;
  assignment: string;
  calling_station_id: string;
  deviceType: string;
  experienceEndTime: number;
  framed_ip_address: string;
  nas_ip_address: string;
  user_name: string;
}

export interface ConnectErrorResponse {
  createdAt: number;
  error: number;
  errorDescription: string;
  statusCode: number;
  token: string;
  truncated: boolean;
}
export interface ConnectResponse {
  createdAt: number;
  error: number;
  errorDescription: string;
  session: Session;
  statusCode: number;
  token: string;
  truncated: boolean;
}

export interface UserSessionInfo {
  username: string;
  mac: string;
}
export interface DeleteMacParams {
  username: string;
  mac?: string;
}
/** 在线会话列表配置信息 */
export interface ConcurrentInfo {
  concurrency: string;
  sessions: Session[];
}

export interface PlanInfo {
  uid: string;
  productName: string;
  pid: string;
  sid: string;
  oid: string;
  source: string;
  createdAt: Date;
  startAt: Date;
  expireAt: Date;
  discontinueAt: Date;
  isp: string;
  status: string;
  flowQuota: number;
  timeQuota: number;
  duration: number;
  bandwidth: number;
  proxyPlan: boolean;
  alias: string;
  needIspBind: boolean;
  inputBandwidth: number;
  default_plan: boolean;
  timeRemaining: number;
  flowRemaining: number;
}

export interface OfflineParams {
  acctUniqueId: string;
  mac: string;
}

export interface noSenseInfo {
  id: string;
  ip: string;
  mac: string;
  macbindAt: string;
  os: string;
  password: string;
  username: string;
  version: string;
}

export interface getMacParam {
  deviceType: string;
  mac: string;
  username: string;
}

export interface saveMacParam {
  mac: string;
  password: string;
  username: string;
  deviceType: string;
}

export interface ConnectBySmsCodeParams {
  webAuthUser: string;
  verifyCode: string;
  deviceType: string;
  redirectUrl: string;
}

export interface CheckWhoAmIBySmsCodeParams {
  webAuthUser: string;
  verifyCode: string;
}

export interface DproxyAccount {
  dproxyISP: 'CTCC' | 'CMCC' | 'CUCC';
  dproxyUsername: string;
}

export interface ConnectByWelinkCodeParams {
  code: string;
  redirectUrl: string;
  deviceType: string;
}

export interface ConnectByUnifiedParams {
  code: string;
  redirectUrl: string;
  deviceType: string;
  appType: string;
}

export interface UpdateRealnameParams {
  idNumber: string;
  realName: string;
}
