export interface UserSessionInfo {
  username: string;
  mac: string;
  framedIPAddress?: string;
  nasPortId?: string;
  acctStartTime?: string;
  uid?: string;
}

export interface DisconnectDeviceParams {
  sessionId: string;
  nasIp: string;
  userIp: string;
}

export interface SessionDevice {
  radAcctId: number;
  acctSessionId: string;
  acctUniqueId: string;
  userName: string;
  username: string;
  groupName?: string;
  realm?: string;
  nasIpAddress: string;
  nasPortId: string;
  nasPortType: string;
  acctStartTime: string;
  acctUpdateTime: string;
  acctStopTime?: string;
  acctInterval: number;
  acctSessionTime: number;
  acctAuthentic: string;
  connectInfoStart: string;
  connectInfoStop?: string;
  acctInputOctets: number;
  acctOutputOctets: number;
  calledStationId: string;
  callingStationId: string;
  mac: string;
  acctTerminateCause?: string;
  serviceType: string;
  framedProtocol: string;
  framedIPAddress: string;
  framedIPV6Address?: string;
  framedIPV6Prefix?: string;
  framedInterFaceid?: string;
  delegatedIPV6Prefix?: string;
  device: string;
  uid: string;
  xProductBandwidth: string;
  xProductInputBandwidth: string;
  xProductId: string;
  xProductPriority: string;
}
