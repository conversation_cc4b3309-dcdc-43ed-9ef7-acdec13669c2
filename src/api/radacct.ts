import axios from '@/http/axios';
import type { DisconnectDeviceParams, SessionDevice, UserSessionInfo } from './radacctData';

interface RadacctCommonParams {
  userIp?: string;
  username?: string;
}

export const getUserSessionInfo = (params: RadacctCommonParams) => axios.post<UserSessionInfo>('/radacct-service/api/v2/session/me/online', params);
export const disconnect = (params: RadacctCommonParams) => axios.post('/radacct-service/api/v2/disconnect/me', params);

export const getDeviceList = (params: RadacctCommonParams) => axios.post<SessionDevice[]>('/radacct-service/api/v2/session/me/all', params);

export const disconnectDevice = ({ sessionId, nasIp, userIp }: DisconnectDeviceParams) =>
  axios.post(`/radacct-service/api/v2/disconnect/request`, {
    userRequestList: [
      {
        acctSessionId: sessionId,
        userIp: userIp,
        nasIp: nasIp,
      },
    ],
  });

export default {};
