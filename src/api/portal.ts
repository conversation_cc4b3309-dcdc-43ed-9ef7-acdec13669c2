import axios from '@/http/axios';
import type {
  CheckWhoAmIBySmsCodeParams,
  ConcurrentInfo,
  ConnectBySmsCodeParams,
  ConnectByUnifiedParams,
  ConnectByWelinkCodeParams,
  ConnectErrorResponse,
  ConnectParams,
  ConnectResponse,
  DeleteMacParams,
  DproxyAccount,
  getMacParam,
  noSenseInfo,
  OfflineParams,
  PlanInfo,
  saveMacParam,
  UpdateRealnameParams,
} from './portalData';
import { AxiosPromise } from 'axios';

type ConnectR = ConnectResponse | ConnectErrorResponse;

// 认证

export const connect = (params: ConnectParams) =>
  // @ts-ignore
  axios<ConnectResponse>({
    method: 'post',
    url: '/portal-conversion/api/v3/portal/connect',
    data: params,
    repeatable: true,
    requestId: 'connect',
  }) as AxiosPromise<ConnectR>;

export const reConnect = () => axios.post('/portal-conversion/api/v3/portal/connect/token', '');

export const deleteMac = (params: DeleteMacParams) => axios.post('/portal-conversion/api/v3/mac/deleteMac', params);

export const getPlanService = () => axios.post<PaginationResponse<PlanInfo>>('/portal-conversion/api/v3/session/plans', '');
/** 获取所有在线会话列表 */
export const getSessionList = () => axios.post<ConcurrentInfo>('/portal-conversion/api/v3/session/list', '');

export const offline = (params: OfflineParams) => axios.post(`/portal-conversion/api/v3/session/acctUniqueId`, params);

export const getMacList = (param: getMacParam) => axios.post<noSenseInfo[]>('/portal-conversion/api/v3/mac/getMacList', param);

export const saveMac = (param: saveMacParam) => axios.post('/portal-conversion/api/v3/mac/saveMac', param);

export const checkWhoAmI = (params: Record<string, string>) => axios.post('/portal-conversion/api/v3/portal/verify', params);

export const sendVerifyCode = (username: string) => axios.post(`/portal-conversion/api/v3/sms/send/verifyCode`, { phone: username });

export const connectBySmsCode = (params: ConnectBySmsCodeParams) =>
  axios.post<ConnectResponse | ConnectErrorResponse>(`/portal-conversion/api/v3/sms/check/verifyCode`, params);

export const checkWhoAmIBySmsCode = (params: CheckWhoAmIBySmsCodeParams) => axios.post(`/portal-conversion/api/v3/sms/verify/user`, params);

export const getMultiDproxyAccounts = (params: Pick<ConnectBySmsCodeParams, 'webAuthUser'>) =>
  axios.post<DproxyAccount[]>(`/portal-conversion/api/v3/session/mult/dproxy/account`, params);

export const checkPassword = (params: Record<string, string>) =>
  axios.post<ConnectResponse | ConnectErrorResponse>('/portal-conversion/api/v3/portal/check', params);

// 通过旧密码修改密码
export const resetPasswordByOldPassword = (params: { webAuthUser: string; webAuthPassword: string; webAuthNewPassword: string }) =>
  axios.post('/portal-conversion/api/v3/portal/changePasswd', params);

export const checkRealname = () => axios.post<boolean>('/portal-conversion/api/v3/realname/check');

export const updateRealname = (params: UpdateRealnameParams) => axios.post('/portal-conversion/api/v3/realname/update/info', params);

export const connectByWelinkCode = (params: ConnectByWelinkCodeParams) =>
  axios.post<ConnectResponse | ConnectErrorResponse>(`/portal-conversion/api/v3/app/online`, {
    ...params,
    appType: 'WELINK',
  });

export const connectByUnified = (params: ConnectByUnifiedParams) =>
  axios.post<ConnectResponse | ConnectErrorResponse>('/portal-conversion/api/v3/app/online', params);

/**
 * 检测手机号是否已绑定
 */
export const checkMobileBinded = (params: { username: string }) => axios.post('/portal-conversion/api/v3/realname/mobile/check', params);

/**
 * 发送绑定手机号验证码
 */
export const sendBindMobileCode = (params: { mobile: string }) => axios.post('/portal-conversion/api/v3/realname/bind/mobile/send/sms', params);

export const bindMobile = (params: { username: string; password: string; mobile: string; code: string }) =>
  axios.post('/portal-conversion/api/v3/realname/bind/mobile', params);

// 检查过期套餐是否为加收套餐
export const checkLastExpiredPlanIsMarkUp = (params: { webAuthUser: string }) =>
  axios.post('/portal-conversion/api/v3/session/check/last/expired/pid/account', params);
