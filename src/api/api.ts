import axios, { axiosPost } from '@/http/axios';
import type { FindPassword, FindPasswordByIdNumber, HandleForm, ObtainVerify } from './apiData';

const newInstance = axios.create();

export const getPortalWebConfigration = () => axiosPost<string>(`/portal-conversion/api/v3/portal/get/jsonConfig?filename=configration.json`, '');
export const getLocalPortalWebConfigration = () => newInstance.get<FullConfig>('/configration.nwupl.json');

export const getMpLink = (params: { redirectUrl: string }) =>
  axios.post<{ redirectWechatUrl: string }>('/portal-conversion/api/v3/wechat/redirect/miniprogram', params);
// 发送验证码
export const sendVerifyButton = (params: ObtainVerify) => {
  const p = new FormData();
  p.append('phone', params.phone);
  p.append('type', 'RETRIEVE');
  return axios.post('/api-service/api/v1/verifyCode2', p);
};

// 下一步按钮
export const verifyCodeButton = (params: HandleForm) => {
  const p = new FormData();
  p.append('phone', params.phone);
  p.append('type', params.type);
  p.append('verifyCode', params.verifyCode);
  return axios.post('/api-service/api/v1/verify', p);
};

// 使用手机号找回密码
export const resetPassword = (params: FindPassword) => {
  const p = new FormData();
  p.append('user', params.user);
  p.append('verifyCode', params.verifyCode);
  p.append('password', params.password);
  p.append('confirm', params.confirm);
  return axios.post('/api-service/api/v1/password', p);
};

export const checkIdNumber = ({ user, identification }: Pick<FindPasswordByIdNumber, 'user' | 'identification'>) => {
  const custData = new FormData();
  custData.append('user', user);
  custData.append('identification', identification);
  return axios.post('/api-service/api/v1/verify/identification', custData);
};

export const findPwdsByIdnumber = ({ user, identification, password, confirm }: FindPasswordByIdNumber) => {
  const custData = new FormData();
  custData.append('user', user);
  custData.append('identification', identification);
  custData.append('password', password);
  custData.append('confirm', confirm);
  return axios.post('/api-service/api/v1/identification/password', custData);
};

export default {};
